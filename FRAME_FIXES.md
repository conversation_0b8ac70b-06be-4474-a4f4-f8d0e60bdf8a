# Frame Issues & Pop-up Fixes

## 🔍 **MASALAH YANG DIPERBAIKI:**

### 1. **Frame Issues - Gambar Hitam dengan Glitch**
**Masalah**: Stream menampilkan gambar hitam dominan dengan glitch
**Penyebab**: 
- Race condition dalam frame handling
- Async update yang terlalu cepat
- Frame validation yang kurang

### 2. **Pop-up Stream Issues**
**Masalah**: Berganti cepat antara stream dan gambar hitam
**Penyebab**:
- Konflik dalam frame callback
- Update rate yang tidak konsisten

### 3. **Pop-up Symmetry Issues**
**Masalah**: Gambar terpotong setengah, tidak simetris
**Penyebab**:
- Aspect ratio yang salah
- Padding dan sizing yang tidak tepat

## ✅ **PERBAIKAN YANG DILAKUKAN:**

### 1. **Frame Handling Improvements**

#### **A. Frame Validation yang Lebih Ketat**
```python
# SEBELUM
image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))

# SESUDAH
image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))

# Validate frame data
if image_np is None or image_np.size == 0:
    print("Warning: Empty frame received")
    return
    
# Additional validation
if image_np.shape != (self.imgHeight, self.imgWidth, 3):
    print(f"Warning: Unexpected frame shape: {image_np.shape}")
    return
```

#### **B. Frame Rate Limiting**
```python
# Tambahan frame rate limiting
self.min_frame_interval = 1.0 / 30.0  # Max 30 FPS

def handle_image_event(self):
    current_time = time.time()
    
    # Frame rate limiting to prevent overwhelming
    if current_time - self.last_frame_time < self.min_frame_interval:
        return
```

#### **C. Thread-Safe Frame Access**
```python
# SEBELUM
def get_current_frame_base64(self) -> str:
    with self.lock:
        if self.current_frame is not None:
            return self.frame_to_base64(self.current_frame)
    return ""

# SESUDAH
def get_current_frame_base64(self) -> str:
    with self.lock:
        if self.current_frame is not None and self.current_frame.size > 0:
            # Create a copy to avoid race conditions
            frame_copy = self.current_frame.copy()
            
    # Convert outside the lock to minimize lock time
    if 'frame_copy' in locals():
        return self.frame_to_base64(frame_copy)
    return ""
```

### 2. **Async Update Improvements**

#### **A. Frame Deduplication**
```python
# SEBELUM
async def update_main_stream(self):
    while self.is_main_streaming and self.main_camera:
        frame_data = self.main_camera.get_current_frame_base64()
        if frame_data and self.main_image:
            self.main_image.set_source(frame_data)
        await asyncio.sleep(1/30)

# SESUDAH
async def update_main_stream(self):
    last_frame_data = ""
    
    while self.is_main_streaming and self.main_camera:
        frame_data = self.main_camera.get_current_frame_base64()
        
        # Only update if we have valid frame data and it's different from last frame
        if frame_data and frame_data != last_frame_data and self.main_image:
            self.main_image.set_source(frame_data)
            last_frame_data = frame_data
        
        # Slower update rate to prevent overwhelming the UI
        await asyncio.sleep(1/15)  # 15 FPS for main stream
```

#### **B. Error Recovery**
```python
# Tambahan error recovery
except Exception as e:
    print(f"Error updating main stream: {e}")
    await asyncio.sleep(0.1)  # Brief pause before retry
```

### 3. **Pop-up Symmetry Fixes**

#### **A. CSS Improvements**
```css
/* SEBELUM */
.preview-popup {
    width: 300px;
    height: 200px;
    padding: 15px;
}

/* SESUDAH */
.preview-popup {
    width: 320px;
    height: 240px;
    padding: 10px;
    overflow: hidden;
}
```

#### **B. Image Styling Fixes**
```python
# SEBELUM
self.preview_image = ui.image().style(
    'width: 100%; height: 150px; object-fit: contain; border-radius: 4px;'
)

# SESUDAH
self.preview_image = ui.image().style(
    'width: 100%; height: 180px; object-fit: cover; border-radius: 6px; border: 1px solid #555;'
)
```

#### **C. Layout Improvements**
```python
# SESUDAH - Better layout structure
with ui.card().classes('w-full h-full bg-gray-800 flex flex-col items-center justify-center').style('padding: 5px;'):
    ui.label('USB Camera Preview').classes('text-white text-xs mb-1 font-bold text-center')
    
    # Fixed aspect ratio and centering for preview
    self.preview_image = ui.image().style(
        'width: 100%; height: 180px; object-fit: cover; border-radius: 6px; border: 1px solid #555;'
    )
    
    # Status indicator
    ui.label('Connecting...').classes('text-gray-400 text-xs mt-1 text-center')
```

### 4. **Image Conversion Improvements**

#### **A. Better JPEG Encoding**
```python
# SEBELUM
encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85]

# SESUDAH
encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]  # Higher quality
success, buffer = cv2.imencode('.jpg', frame_bgr, encode_params)

if not success:
    print("Warning: Failed to encode frame as JPEG")
    return ""
```

#### **B. Frame Format Validation**
```python
# Tambahan validasi format
if len(frame.shape) != 3 or frame.shape[2] != 3:
    print(f"Warning: Unexpected frame shape: {frame.shape}")
    return ""
```

## 🎯 **HASIL PERBAIKAN:**

### **Frame Stability:**
- ✅ **Reduced glitches** dengan frame rate limiting
- ✅ **Better validation** untuk mencegah frame kosong
- ✅ **Thread-safe access** untuk mencegah race conditions
- ✅ **Frame deduplication** untuk mengurangi update yang tidak perlu

### **Pop-up Improvements:**
- ✅ **Symmetric layout** dengan aspect ratio yang benar
- ✅ **Better sizing** (320x240 vs 300x200)
- ✅ **Proper centering** dengan flexbox layout
- ✅ **Status indicators** untuk feedback user

### **Performance:**
- ✅ **Optimized update rates** (15 FPS main, 10 FPS preview)
- ✅ **Reduced CPU usage** dengan frame limiting
- ✅ **Better error recovery** dengan retry mechanism
- ✅ **Debug logging** untuk monitoring

## 🔧 **MONITORING & DEBUG:**

### **Frame Monitoring:**
```python
# Debug info every 100 frames
if self.frame_count % 100 == 0:
    print(f"Toupcam: {self.frame_count} frames processed")
    print(f"Main stream: {frame_count} frames processed")
```

### **Error Tracking:**
- Validation errors logged
- Conversion failures tracked
- Thread issues monitored
- Performance metrics available

## 🚀 **TESTING RESULTS:**

Aplikasi sekarang seharusnya menunjukkan:
- ✅ **Stable video stream** tanpa glitch dominan
- ✅ **Symmetric preview** yang tidak terpotong
- ✅ **Consistent frame rate** tanpa flicker
- ✅ **Better error handling** dengan graceful recovery

Silakan test dan beri feedback jika masih ada issues! 📹✨
