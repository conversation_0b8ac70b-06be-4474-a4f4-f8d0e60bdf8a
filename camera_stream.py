#!/usr/bin/env python3
"""
Camera Streaming Module for NiceGUI
Converted from PyQt implementation to work with NiceGUI
"""

import cv2
import asyncio
import threading
import base64
import time
from typing import Optional, Callable
from nicegui import ui
import numpy as np

class CameraStream:
    def __init__(self, camera_index: int = 0):
        """
        Initialize camera stream
        
        Args:
            camera_index: Camera device index (default: 0)
        """
        self.camera_index = camera_index
        self.cap: Optional[cv2.VideoCapture] = None
        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.frame_callback: Optional[Callable] = None
        self.current_frame = None
        self.fps = 30
        self.frame_width = 640
        self.frame_height = 480
        
        # Threading control
        self.lock = threading.Lock()
        self.stop_event = threading.Event()
        
    def initialize_camera(self) -> bool:
        """
        Initialize camera capture
        
        Returns:
            bool: True if camera initialized successfully
        """
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                print(f"Error: Cannot open camera {self.camera_index}")
                return False
                
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            print(f"Camera {self.camera_index} initialized successfully")
            print(f"Resolution: {self.frame_width}x{self.frame_height}")
            print(f"FPS: {self.fps}")
            
            return True
            
        except Exception as e:
            print(f"Error initializing camera: {e}")
            return False
    
    def set_frame_callback(self, callback: Callable):
        """
        Set callback function to receive frames
        
        Args:
            callback: Function to call with each frame
        """
        self.frame_callback = callback
    
    def frame_to_base64(self, frame) -> str:
        """
        Convert OpenCV frame to base64 string for web display
        
        Args:
            frame: OpenCV frame (numpy array)
            
        Returns:
            str: Base64 encoded image string
        """
        try:
            # Encode frame as JPEG
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            # Convert to base64
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return f"data:image/jpeg;base64,{img_base64}"
            
        except Exception as e:
            print(f"Error converting frame to base64: {e}")
            return ""
    
    def capture_loop(self):
        """
        Main camera capture loop (runs in separate thread) - SIMPLIFIED
        """
        frame_time = 1.0 / self.fps

        while not self.stop_event.is_set() and self.is_running:
            start_time = time.time()

            try:
                if self.cap is None or not self.cap.isOpened():
                    print("USB Camera: Camera not available")
                    break

                ret, frame = self.cap.read()
                if not ret:
                    print("USB Camera: Failed to read frame")
                    time.sleep(0.1)
                    continue

                # Store current frame with thread safety
                with self.lock:
                    self.current_frame = frame.copy()

                # Control frame rate
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_time - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)

            except Exception as e:
                print(f"Error in USB camera capture loop: {e}")
                time.sleep(0.1)

        print("USB Camera capture loop ended")
    
    def start_stream(self) -> bool:
        """
        Start camera streaming
        
        Returns:
            bool: True if stream started successfully
        """
        if self.is_running:
            print("Camera stream is already running")
            return True
            
        if not self.initialize_camera():
            return False
        
        self.is_running = True
        self.stop_event.clear()
        
        # Start capture thread
        self.thread = threading.Thread(target=self.capture_loop, daemon=True)
        self.thread.start()
        
        print("Camera stream started")
        return True
    
    def stop_stream(self):
        """
        Stop camera streaming
        """
        if not self.is_running:
            return
            
        print("Stopping camera stream...")
        
        # Signal thread to stop
        self.is_running = False
        self.stop_event.set()
        
        # Wait for thread to finish
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        
        # Release camera
        if self.cap:
            self.cap.release()
            self.cap = None
        
        print("Camera stream stopped")
    
    def get_current_frame_base64(self) -> str:
        """
        Get current frame as base64 string (thread-safe)
        
        Returns:
            str: Base64 encoded current frame
        """
        with self.lock:
            if self.current_frame is not None:
                return self.frame_to_base64(self.current_frame)
        return ""
    
    def set_resolution(self, width: int, height: int):
        """
        Set camera resolution
        
        Args:
            width: Frame width
            height: Frame height
        """
        self.frame_width = width
        self.frame_height = height
        
        if self.cap and self.cap.isOpened():
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
    
    def set_fps(self, fps: int):
        """
        Set camera FPS
        
        Args:
            fps: Frames per second
        """
        self.fps = fps
        
        if self.cap and self.cap.isOpened():
            self.cap.set(cv2.CAP_PROP_FPS, fps)
    
    def __del__(self):
        """
        Destructor - ensure camera is properly released
        """
        self.stop_stream()


class NiceGUICameraDisplay:
    def __init__(self, camera_stream: CameraStream):
        """
        NiceGUI camera display component
        
        Args:
            camera_stream: CameraStream instance
        """
        self.camera_stream = camera_stream
        self.image_element = None
        self.is_updating = False
        
    def create_display(self, width: str = "100%", height: str = "100%"):
        """
        Create camera display element
        
        Args:
            width: CSS width
            height: CSS height
            
        Returns:
            ui.image: NiceGUI image element
        """
        # Create image element for camera display
        self.image_element = ui.image().style(f'width: {width}; height: {height}; object-fit: contain;')
        
        # Set up frame callback
        self.camera_stream.set_frame_callback(self.update_frame)
        
        return self.image_element
    
    def update_frame(self, frame):
        """
        Update frame in NiceGUI (called from camera thread)
        
        Args:
            frame: OpenCV frame
        """
        if self.image_element and not self.is_updating:
            self.is_updating = True
            try:
                # Convert frame to base64
                base64_image = self.camera_stream.frame_to_base64(frame)
                
                # Update image element (this needs to be done in main thread)
                if base64_image:
                    ui.run_javascript(f'''
                        const img = document.querySelector('img[src*="data:image"]');
                        if (img) img.src = "{base64_image}";
                    ''')
                    
            except Exception as e:
                print(f"Error updating frame: {e}")
            finally:
                self.is_updating = False
