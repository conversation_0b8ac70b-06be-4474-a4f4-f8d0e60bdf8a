# Toupcam SDK Integration Guide - CORRECTED

## Overview
Implementasi Toupcam SDK untuk main camera stream dalam aplikasi Slide Scanner, dikonversi dari PyQt ke NiceGUI dengan mengikuti pola yang BENAR dari `main_cam.py`.

## ✅ PERBAIKAN YANG DILAKUKAN:

### 1. **Mengikuti Pola main_cam.py dengan Tepat**
- ✅ **Event Callback**: Menggunakan `StartPullModeWithCallback` dengan callback yang benar
- ✅ **Buffer Allocation**: Menggunakan `TDIBWIDTHBYTES` seperti di main_cam.py
- ✅ **Error Handling**: Menggunakan `toupcam.HRESULTException` yang spesifik
- ✅ **Threading**: FPS monitoring dengan proper thread management
- ✅ **Camera Configuration**: Sama persis dengan main_cam.py

### 2. **Callback Mechanism yang Benar**
```python
# PyQt (main_cam.py)
@staticmethod
def eventCallBack(nEvent, self_obj):
    if self_obj and self_obj.is_running:
         self_obj.onevtCallback(nEvent)

# NiceGUI (toupcam_stream.py) - CORRECTED
@staticmethod
def event_callback(nEvent, self_obj):
    if self_obj and self_obj.is_running:
        self_obj.handle_event(nEvent)
```

### 3. **Buffer Management yang Tepat**
```python
# Exactly like main_cam.py
buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
self.pData = bytes(buffer_size)
```

## File Structure

```
Slide Scanner/
├── main_cam.py           # Original PyQt Toupcam implementation
├── toupcam_stream.py     # New NiceGUI Toupcam wrapper
├── camera_integration.py # Camera manager with dual camera support
└── UI.py                 # Updated UI with auto-connect
```

## Key Features

### 1. **Auto-Connect on Startup**
- Main camera (Toupcam) connects automatically when app starts
- No manual "Start Camera" button needed
- Graceful fallback if camera not available

### 2. **Threading Architecture**
```
Main Thread (NiceGUI)
├── UI Updates (asyncio)
├── User Interactions
└── Camera Manager

Toupcam Thread
├── SDK Event Callbacks
├── Frame Processing
├── FPS Monitoring
└── Base64 Conversion

USB Camera Thread
├── OpenCV Capture Loop
├── Frame Processing
└── Preview Updates
```

### 3. **Converted PyQt → NiceGUI**

#### PyQt Signal/Slot → NiceGUI Callback
```python
# PyQt (Original)
frame_ready = pyqtSignal(QPixmap)
fps_updated = pyqtSignal(str)

# NiceGUI (New)
self.frame_callback = callback_function
self.fps_callback = callback_function
```

#### PyQt QImage → NiceGUI Base64
```python
# PyQt (Original)
image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
pixmap = QPixmap.fromImage(image)
self.frame_ready.emit(pixmap)

# NiceGUI (New)
image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))
base64_data = self.frame_to_base64(image_np)
ui.image.set_source(base64_data)
```

## Implementation Details

### 1. **ToupcamStream Class** (`toupcam_stream.py`)

#### Key Methods:
- `initialize_camera()`: Find and open Toupcam
- `start_stream()`: Begin streaming with callbacks
- `handle_image_event()`: Process new frames
- `frame_to_base64()`: Convert for web display
- `snap_image()`: Capture still image

#### Threading Safety:
```python
# Thread-safe frame access
with self.lock:
    self.current_frame = image_np.copy()
```

### 2. **Camera Manager Integration**

#### Auto-Start Sequence:
```python
# UI.py - Auto-start timer
ui.timer(1.0, self.auto_start_main_camera, once=True)

# camera_integration.py - Auto-start method
def auto_start_main_camera(self):
    self.start_main_camera()
```

#### Dual Camera Support:
- **Main**: Toupcam SDK (scientific camera)
- **Preview**: OpenCV USB camera (standard webcam)
- **Independent**: Each camera runs in separate thread

### 3. **UI Integration**

#### Compact Menu Bar:
```python
# Smaller menu bar (45px height)
with ui.header().classes('bg-gray-800 text-white').style('height: 45px; min-height: 45px;'):
    # Smaller dropdown buttons
    with ui.dropdown_button('Camera', icon='videocam', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
```

#### Camera Controls:
- **Capture Image**: Primary function in Camera menu
- **Restart Cameras**: Manual restart if needed
- **No Start/Stop**: Auto-connect eliminates need

## Error Handling

### 1. **SDK Not Available**
```python
try:
    from Camera import toupcam
    TOUPCAM_AVAILABLE = True
except ImportError:
    print("Warning: Toupcam SDK not found. Main camera will not work.")
    TOUPCAM_AVAILABLE = False
```

### 2. **Camera Not Found**
```python
arr = toupcam.Toupcam.EnumV2()
if not arr:
    print("No Toupcam cameras found")
    return False
```

### 3. **Streaming Errors**
```python
try:
    self.hcam.PullImageV4(self.pData, 0, 24, 0, None)
    # Process frame...
except Exception as e:
    print(f"Error in Toupcam handleImageEvent: {e}")
```

## Configuration

### 1. **Camera Settings**
```python
# Real-time mode for low latency
self.hcam.put_RealTime(1)

# Vertical flip
self.hcam.put_VFlip(1)

# Byte order
self.hcam.put_Option(toupcam.TOUPCAM_OPTION_BYTEORDER, 0)
```

### 2. **Performance Optimization**
```python
# Buffer allocation
buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
self.pData = bytes(buffer_size)

# FPS monitoring
def fps_update():
    nFrame, nTime, nTotalFrame = self.hcam.get_FrameRate()
    fps_val = (nFrame * 1000.0 / nTime) if nTime > 0 else 0.0
```

## Usage

### 1. **Automatic Operation**
- Start app: `python main.py`
- Cameras connect automatically
- Main stream appears immediately
- Preview available via View menu

### 2. **Manual Controls**
```
Camera Menu:
├── Capture Image        # Primary function
├── Stop Toupcam        # If needed
├── Restart Toupcam     # If needed
├── Stop USB Preview    # If needed
└── Restart USB Preview # If needed
```

### 3. **Status Monitoring**
- **Main Camera**: Status and FPS in overlay
- **Preview Camera**: Auto-start notification
- **Error Messages**: Real-time notifications

## Troubleshooting

### 1. **Toupcam Not Detected**
```
Issue: "No Toupcam cameras found"
Solution: 
- Check camera connection
- Install Toupcam drivers
- Verify SDK installation
```

### 2. **Performance Issues**
```
Issue: Slow frame rate, high CPU
Solution:
- Check RealTime mode setting
- Verify buffer allocation
- Monitor thread performance
```

### 3. **Threading Problems**
```
Issue: UI freezing, frame drops
Solution:
- Check callback functions
- Verify async/await usage
- Monitor thread lifecycle
```

## Next Steps

1. **Test with actual Toupcam hardware**
2. **Verify SDK compatibility**
3. **Optimize performance settings**
4. **Add advanced camera controls**
5. **Implement image saving functionality**

## Dependencies

```
# Required
nicegui>=1.4.0
opencv-python>=4.8.0
numpy>=1.24.0

# Toupcam SDK (install separately)
# Download from manufacturer website
```
