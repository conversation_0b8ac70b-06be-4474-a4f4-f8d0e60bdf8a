#!/usr/bin/env python3
"""
Stream Generator for NiceGUI - FIXED for glitch issues
Using generator approach instead of .set_source() per frame
"""

import asyncio
import threading
import base64
import time
import cv2
import numpy as np
from typing import Optional, Generator
from toupcam_stream import ToupcamStream
from camera_stream import CameraStream

class StreamGenerator:
    def __init__(self):
        """Initialize stream generator"""
        self.toupcam_stream = None
        self.usb_stream = None
        
        # Stream switching
        self.main_source = "toupcam"  # "toupcam" or "usb"
        self.preview_source = "usb"   # "toupcam" or "usb"
        
        # Generator control
        self.main_generator_active = False
        self.preview_generator_active = False
        
        # Frame caching
        self.last_toupcam_frame = None
        self.last_usb_frame = None
        
        # Threading
        self.lock = threading.Lock()
        
    def initialize_streams(self):
        """Initialize both camera streams"""
        try:
            # Initialize Toupcam
            self.toupcam_stream = ToupcamStream()
            if self.toupcam_stream.initialize_camera():
                self.toupcam_stream.start_stream()
                print("✅ Toupcam stream initialized")
            else:
                print("❌ Toupcam initialization failed")
                
            # Initialize USB camera
            self.usb_stream = CameraStream(0)
            if self.usb_stream.start_stream():
                print("✅ USB camera stream initialized")
            else:
                print("❌ USB camera initialization failed")
                
            return True
            
        except Exception as e:
            print(f"Error initializing streams: {e}")
            return False
    
    def get_toupcam_frame(self) -> Optional[str]:
        """Get current Toupcam frame as base64"""
        if self.toupcam_stream:
            frame_data = self.toupcam_stream.get_current_frame_base64()
            if frame_data:
                with self.lock:
                    self.last_toupcam_frame = frame_data
                return frame_data
        
        # Return last known good frame if current is empty
        with self.lock:
            return self.last_toupcam_frame
    
    def get_usb_frame(self) -> Optional[str]:
        """Get current USB camera frame as base64"""
        if self.usb_stream:
            frame_data = self.usb_stream.get_current_frame_base64()
            if frame_data:
                with self.lock:
                    self.last_usb_frame = frame_data
                return frame_data
        
        # Return last known good frame if current is empty
        with self.lock:
            return self.last_usb_frame
    
    def main_stream_generator(self) -> Generator[str, None, None]:
        """Generator for main stream - NO MORE .set_source() per frame!"""
        self.main_generator_active = True
        frame_count = 0
        
        while self.main_generator_active:
            try:
                # Get frame based on current main source
                if self.main_source == "toupcam":
                    frame_data = self.get_toupcam_frame()
                else:  # usb
                    frame_data = self.get_usb_frame()
                
                # Only yield if we have valid frame data
                if frame_data:
                    yield frame_data
                    frame_count += 1
                    
                    # Debug info
                    if frame_count % 100 == 0:
                        print(f"Main stream generator: {frame_count} frames ({self.main_source})")
                
                # Control frame rate - 20 FPS for smooth streaming
                time.sleep(1/20)
                
            except Exception as e:
                print(f"Error in main stream generator: {e}")
                time.sleep(0.1)
        
        print("Main stream generator stopped")
    
    def preview_stream_generator(self) -> Generator[str, None, None]:
        """Generator for preview stream - NO MORE .set_source() per frame!"""
        self.preview_generator_active = True
        frame_count = 0
        
        while self.preview_generator_active:
            try:
                # Get frame based on current preview source
                if self.preview_source == "toupcam":
                    frame_data = self.get_toupcam_frame()
                else:  # usb
                    frame_data = self.get_usb_frame()
                
                # Only yield if we have valid frame data
                if frame_data:
                    yield frame_data
                    frame_count += 1
                    
                    # Debug info
                    if frame_count % 50 == 0:
                        print(f"Preview stream generator: {frame_count} frames ({self.preview_source})")
                
                # Control frame rate - 15 FPS for preview
                time.sleep(1/15)
                
            except Exception as e:
                print(f"Error in preview stream generator: {e}")
                time.sleep(0.1)
        
        print("Preview stream generator stopped")
    
    def switch_streams(self):
        """Switch main and preview streams"""
        print(f"🔄 Switching streams...")
        print(f"   Before: Main={self.main_source}, Preview={self.preview_source}")
        
        # Swap sources
        old_main = self.main_source
        old_preview = self.preview_source
        
        self.main_source = old_preview
        self.preview_source = old_main
        
        print(f"   After: Main={self.main_source}, Preview={self.preview_source}")
        print(f"✅ Stream switch completed!")
        
        return {
            'main_source': self.main_source,
            'preview_source': self.preview_source
        }
    
    def get_stream_status(self) -> dict:
        """Get current stream status"""
        return {
            'main_source': self.main_source,
            'preview_source': self.preview_source,
            'toupcam_active': self.toupcam_stream is not None and self.toupcam_stream.is_running,
            'usb_active': self.usb_stream is not None and self.usb_stream.is_running,
            'main_generator_active': self.main_generator_active,
            'preview_generator_active': self.preview_generator_active
        }
    
    def stop_generators(self):
        """Stop all generators"""
        self.main_generator_active = False
        self.preview_generator_active = False
        
        if self.toupcam_stream:
            self.toupcam_stream.stop_stream()
        
        if self.usb_stream:
            self.usb_stream.stop_stream()
        
        print("All stream generators stopped")

# Global stream generator instance
stream_generator = StreamGenerator()

def get_stream_generator():
    """Get the global stream generator instance"""
    return stream_generator

def initialize_stream_system():
    """Initialize the stream system"""
    return stream_generator.initialize_streams()

# Generator functions for NiceGUI
def main_stream_source():
    """Main stream source for NiceGUI"""
    return stream_generator.main_stream_generator()

def preview_stream_source():
    """Preview stream source for NiceGUI"""
    return stream_generator.preview_stream_generator()

def switch_camera_streams():
    """Switch main and preview camera streams"""
    return stream_generator.switch_streams()

def get_current_stream_status():
    """Get current stream configuration"""
    return stream_generator.get_stream_status()
