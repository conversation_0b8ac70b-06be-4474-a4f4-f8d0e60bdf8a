#!/usr/bin/env python3
"""
Toupcam Streaming Module for NiceGUI
Converted from PyQt implementation to work with NiceGUI
"""

import asyncio
import threading
import base64
import time
import numpy as np
from typing import Optional, Callable
from nicegui import ui

try:
    from Camera import toupcam
    TOUPCAM_AVAILABLE = True
except ImportError:
    print("Warning: Toupcam SDK not found. Main camera will not work.")
    TOUPCAM_AVAILABLE = False

class ToupcamStream:
    def __init__(self):
        """Initialize Toupcam stream"""
        self.hcam = None
        self.pData = None
        self.imgWidth = 0
        self.imgHeight = 0
        self.is_running = False
        self.cur = None
        self.res = 0
        
        # Threading control
        self.lock = threading.Lock()
        self.frame_callback: Optional[Callable] = None
        self.current_frame = None
        self.current_pixmap = None
        
        # FPS tracking
        self.fps_timer = None
        self.fps_callback: Optional[Callable] = None
        
    def set_frame_callback(self, callback: Callable):
        """Set callback function to receive frames"""
        self.frame_callback = callback
    
    def set_fps_callback(self, callback: Callable):
        """Set callback function to receive FPS updates"""
        self.fps_callback = callback
    
    def initialize_camera(self) -> bool:
        """Initialize Toupcam camera"""
        if not TOUPCAM_AVAILABLE:
            print("Toupcam SDK not available")
            return False
            
        try:
            # Find cameras
            arr = toupcam.Toupcam.EnumV2()
            if not arr:
                print("No Toupcam cameras found")
                return False
            
            # Open first camera
            self.cur = arr[0]
            self.hcam = toupcam.Toupcam.Open(self.cur.id)
            
            if not self.hcam:
                print(f"Failed to open camera: {self.cur.displayname}")
                return False
            
            # Set initial resolution
            self.res = self.hcam.get_eSize()
            self.imgWidth = self.cur.model.res[self.res].width
            self.imgHeight = self.cur.model.res[self.res].height
            
            # Configure camera
            self.hcam.put_Option(toupcam.TOUPCAM_OPTION_BYTEORDER, 0)
            
            # Real-time mode for low latency
            try:
                self.hcam.put_RealTime(1)
                print("Toupcam: RealTime mode enabled")
            except Exception as e:
                print(f"Toupcam: Failed to set RealTime mode: {e}")
            
            # Allocate buffer
            buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
            self.pData = bytes(buffer_size)
            
            # Set vertical flip
            self.hcam.put_VFlip(1)
            
            print(f"Toupcam initialized: {self.cur.displayname}")
            print(f"Resolution: {self.imgWidth}x{self.imgHeight}")
            
            return True
            
        except Exception as e:
            print(f"Error initializing Toupcam: {e}")
            return False
    
    def start_stream(self) -> bool:
        """Start Toupcam streaming"""
        if not self.hcam:
            return False
            
        if self.is_running:
            return True
        
        try:
            # Start pull mode with callback
            self.hcam.StartPullModeWithCallback(self.event_callback, self)
            self.is_running = True
            
            # Start FPS timer
            self.start_fps_timer()
            
            print("Toupcam stream started")
            return True
            
        except Exception as e:
            print(f"Error starting Toupcam stream: {e}")
            return False
    
    def stop_stream(self):
        """Stop Toupcam streaming"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # Stop FPS timer
        if self.fps_timer:
            self.fps_timer = None
        
        # Close camera
        if self.hcam:
            try:
                self.hcam.Close()
            except:
                pass
            self.hcam = None
        
        self.pData = None
        print("Toupcam stream stopped")
    
    def start_fps_timer(self):
        """Start FPS monitoring timer"""
        def fps_update():
            while self.is_running and self.hcam:
                try:
                    nFrame, nTime, nTotalFrame = self.hcam.get_FrameRate()
                    fps_val = (nFrame * 1000.0 / nTime) if nTime > 0 else 0.0
                    fps_text = f"FPS: {fps_val:.1f}, Total: {nTotalFrame}"
                    
                    if self.fps_callback:
                        self.fps_callback(fps_text)
                        
                except Exception as e:
                    print(f"Error getting FPS: {e}")
                
                time.sleep(1.0)  # Update every second
        
        # Start FPS monitoring in separate thread
        fps_thread = threading.Thread(target=fps_update, daemon=True)
        fps_thread.start()
    
    @staticmethod
    def event_callback(nEvent, self_obj):
        """Static callback for Toupcam events"""
        if self_obj and self_obj.is_running:
            self_obj.handle_event(nEvent)
    
    def handle_event(self, nEvent):
        """Handle Toupcam events"""
        if not self.is_running:
            return
            
        if nEvent == toupcam.TOUPCAM_EVENT_IMAGE:
            self.handle_image_event()
        elif nEvent == toupcam.TOUPCAM_EVENT_ERROR or nEvent == toupcam.TOUPCAM_EVENT_DISCONNECTED:
            print(f"Toupcam error or disconnected (event: {nEvent})")
            self.stop_stream()
    
    def handle_image_event(self):
        """Handle new image from Toupcam"""
        try:
            # Pull image data
            self.hcam.PullImageV4(self.pData, 0, 24, 0, None)
            
            # Convert to numpy array
            image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))
            
            # Store current frame with thread safety
            with self.lock:
                self.current_frame = image_np.copy()
            
            # Call frame callback if set
            if self.frame_callback:
                try:
                    self.frame_callback(image_np)
                except Exception as e:
                    print(f"Error in Toupcam frame callback: {e}")
                    
        except Exception as e:
            print(f"Error in Toupcam handleImageEvent: {e}")
    
    def frame_to_base64(self, frame) -> str:
        """Convert numpy frame to base64 string for web display"""
        try:
            import cv2
            # Convert RGB to BGR for OpenCV
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            # Encode as JPEG
            _, buffer = cv2.imencode('.jpg', frame_bgr, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            # Convert to base64
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            return f"data:image/jpeg;base64,{img_base64}"
            
        except Exception as e:
            print(f"Error converting Toupcam frame to base64: {e}")
            return ""
    
    def get_current_frame_base64(self) -> str:
        """Get current frame as base64 string (thread-safe)"""
        with self.lock:
            if self.current_frame is not None:
                return self.frame_to_base64(self.current_frame)
        return ""
    
    def snap_image(self, resolution_index: int = 0):
        """Capture still image"""
        if self.hcam and self.is_running:
            try:
                self.hcam.Snap(resolution_index)
                print(f"Toupcam: Snap command sent with resolution index {resolution_index}")
                return True
            except Exception as e:
                print(f"Error taking snap: {e}")
                return False
        return False
    
    def get_camera_info(self) -> dict:
        """Get camera information"""
        if self.cur:
            return {
                'name': self.cur.displayname,
                'width': self.imgWidth,
                'height': self.imgHeight,
                'resolution': f"{self.imgWidth}x{self.imgHeight}",
                'available_resolutions': [f"{res.width}x{res.height}" for res in self.cur.model.res]
            }
        return {}
    
    def set_resolution(self, index: int):
        """Change camera resolution"""
        if not self.hcam or not self.is_running or not self.cur:
            return False
            
        try:
            # Stop temporarily
            self.hcam.Stop()
            
            # Change resolution
            self.res = index
            self.imgWidth = self.cur.model.res[index].width
            self.imgHeight = self.cur.model.res[index].height
            self.hcam.put_eSize(self.res)
            
            # Reallocate buffer
            buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
            self.pData = bytes(buffer_size)
            
            # Restart
            self.hcam.StartPullModeWithCallback(self.event_callback, self)
            print(f"Toupcam: Resolution changed to {self.imgWidth}x{self.imgHeight}")
            return True
            
        except Exception as e:
            print(f"Error changing resolution: {e}")
            return False
    
    def __del__(self):
        """Destructor - ensure camera is properly released"""
        self.stop_stream()
