# Camera Streaming Guide

## Overview
Implementasi camera streaming untuk Slide Scanner menggunakan NiceGUI, dikonversi dari implementasi PyQt sebelumnya.

## Arsitektur

### 1. **CameraStream Class** (`camera_stream.py`)
- **Threading**: Menggunakan `threading.Thread` untuk capture loop
- **Thread Safety**: Menggunakan `threading.Lock` untuk akses frame yang aman
- **Frame Rate Control**: Kontrol FPS dengan sleep timing
- **Base64 Conversion**: Konversi frame OpenCV ke base64 untuk web display

### 2. **CameraManager Class** (`camera_integration.py`)
- **Dual Camera Support**: Main camera dan preview camera
- **Async Updates**: Menggunakan `asyncio` untuk update UI
- **Resolution Control**: Berbeda resolusi untuk main (1280x720) dan preview (640x480)
- **FPS Control**: Main 30fps, Preview 15fps

### 3. **UI Integration** (`UI.py`)
- **Menu Integration**: Menu "Camera" di menu bar
- **Main Stream**: Full screen camera display dengan overlay controls
- **Preview Popup**: Small preview window di kanan atas

## Fitur Camera

### Main Camera
- **Resolution**: 1280x720 (HD)
- **FPS**: 30
- **Controls**: Start/Stop/Capture buttons
- **Status**: Real-time connection status
- **Display**: Full screen dengan object-fit contain

### Preview Camera
- **Resolution**: 640x480 (VGA)
- **FPS**: 15
- **Controls**: Start/Stop buttons
- **Display**: Small popup window

## Threading Architecture

```
Main Thread (NiceGUI)
├── UI Updates (asyncio tasks)
├── User Interactions
└── Camera Manager

Camera Thread (per camera)
├── cv2.VideoCapture loop
├── Frame processing
├── Base64 conversion
└── Callback to UI thread
```

## Cara Penggunaan

### 1. **Melalui Menu Bar**
```
Camera Menu:
├── Start Main Camera
├── Stop Main Camera
├── Start Preview
├── Stop Preview
└── Capture Image
```

### 2. **Melalui Overlay Controls**
- **Main Stream**: Controls di kiri atas main display
- **Preview**: Controls di bawah preview window

### 3. **Melalui View Menu**
- **Toggle Preview**: Show/hide preview popup
- **Toggle Zoom**: Show/hide zoom controls

## Konfigurasi Camera

### Camera Index
```python
# Default configuration
main_camera_index = 0      # Primary camera
preview_camera_index = 0   # Same camera (shared)

# Dual camera setup
main_camera_index = 0      # Primary camera
preview_camera_index = 1   # Secondary camera
```

### Resolution Settings
```python
# Main camera
main_camera.set_resolution(1280, 720)  # HD
main_camera.set_fps(30)

# Preview camera
preview_camera.set_resolution(640, 480)  # VGA
preview_camera.set_fps(15)
```

## Error Handling

### Camera Initialization
- **Auto-retry**: Otomatis retry jika camera gagal initialize
- **Fallback**: Fallback ke camera index lain jika tersedia
- **User Notification**: Notifikasi error ke user

### Streaming Errors
- **Frame Drop**: Skip frame jika processing terlalu lambat
- **Thread Safety**: Lock mechanism untuk prevent race conditions
- **Graceful Shutdown**: Proper cleanup saat stop streaming

## Performance Optimization

### 1. **Frame Rate Control**
```python
frame_time = 1.0 / fps
sleep_time = max(0, frame_time - elapsed)
```

### 2. **Resolution Optimization**
- Main: High resolution untuk detail
- Preview: Low resolution untuk performance

### 3. **Threading**
- Separate thread untuk capture
- Async updates untuk UI
- Non-blocking operations

## Troubleshooting

### Camera Not Found
```
Error: Cannot open camera 0
Solution: Check camera index, try different values (0, 1, 2...)
```

### Low Performance
```
Issue: Choppy video, high CPU usage
Solution: 
- Reduce FPS
- Lower resolution
- Check camera drivers
```

### Threading Issues
```
Issue: UI freezing, frame drops
Solution:
- Check thread synchronization
- Verify async/await usage
- Monitor thread lifecycle
```

## Integration dengan PyQt Code

Jika Anda memiliki code PyQt sebelumnya, berikut mapping konversi:

### PyQt → NiceGUI
```python
# PyQt
QLabel.setPixmap(pixmap)
QTimer.timeout.connect(update_frame)
QThread.start()

# NiceGUI
ui.image.set_source(base64_data)
asyncio.create_task(update_loop())
threading.Thread(target=capture_loop).start()
```

### Signal/Slot → Callback
```python
# PyQt
signal.connect(slot)

# NiceGUI
callback_function()
```

## Next Steps

1. **Copy your PyQt camera code** ke workspace ini
2. **Analyze threading structure** dari code PyQt Anda
3. **Map PyQt components** ke NiceGUI equivalents
4. **Test camera functionality** dengan hardware Anda
5. **Optimize performance** sesuai kebutuhan

## Dependencies
```
opencv-python>=4.8.0
numpy>=1.24.0
nicegui>=1.4.0
```
