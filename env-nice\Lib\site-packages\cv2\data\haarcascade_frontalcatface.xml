<?xml version="1.0"?>
<!--
 A frontal cat face detector using the basic set of Haar features, i.e.
 horizontal and vertical features but not diagonal features.

 Contributed by <PERSON> (joseph<PERSON><PERSON>@nummist.com).

 More information can be found in the following publications and
 presentations:

 <PERSON>. OpenCV for Secret Agents (book). Packt Publishing, January
   2015.
 <PERSON>. "Training Detectors and Recognizers in Python and OpenCV"
   (tutorial). ISMAR 2014. September 9, 2014.
   http://nummist.com/opencv/Howse_ISMAR_20140909.pdf
 <PERSON>. "Training Intelligent Camera Systems with Python and OpenCV"
   (webcast). O’Reilly Media. June 17, 2014.
   http://www.oreilly.com/pub/e/3077

 Build scripts and demo applications can be found in the following repository:
 https://bitbucket.org/<PERSON>_<PERSON><PERSON>/angora-blue

 KNOWN LIMITATIONS:

 An upright subject is assumed. In situations where the cat's face might be
 sideways or upside down (e.g. the cat is rolling over), try various rotations
 of the input image.

 CHANGELOG:

 2016-08-06: Re-trained with more negative samples and more stages. False
   positives are much rarer now. If you tailored your code for the cascade's
   previous version, now you should re-adjust the arguments of
   CascadeClassifier::detectMultiScale. For example, decrease the value of the
   minNeighbors argument. You do not need to use a human face detector to
   cross-check the positives anymore.
 2014-04-25: First release (at https://bitbucket.org/Joe_Howse/angora-blue)

 //////////////////////////////////////////////////////////////////////////
 | Contributors License Agreement
 | IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
 |   By downloading, copying, installing or using the software you agree
 |   to this license.
 |   If you do not agree to this license, do not download, install,
 |   copy or use the software.
 |
 | Copyright (c) 2014-2016, Joseph Howse (Nummist Media Corporation Limited,
 | Halifax, Nova Scotia, Canada). All rights reserved.
 |
 | Redistribution and use in source and binary forms, with or without
 | modification, are permitted provided that the following conditions are
 | met:
 |
 |    * Redistributions of source code must retain the above copyright
 |       notice, this list of conditions and the following disclaimer.
 |    * Redistributions in binary form must reproduce the above
 |      copyright notice, this list of conditions and the following
 |      disclaimer in the documentation and/or other materials provided
 |      with the distribution.
 |    * The name of Contributor may not used to endorse or promote products
 |      derived from this software without specific prior written permission.
 |
 | THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 | "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 | LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 | A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 | CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 | EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 | PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 | PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 | LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 | NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 | SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
 | Top
 //////////////////////////////////////////////////////////////////////////
 -->
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>24</height>
  <width>24</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9500000476837158e-01</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-01</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-01</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount>
    <featSize>1</featSize>
    <mode>BASIC</mode></featureParams>
  <stageNum>20</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>16</maxWeakCount>
      <stageThreshold>-1.4806525707244873e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 472 -1.5126220881938934e-02</internalNodes>
          <leafValues>
            7.5887596607208252e-01 -3.4230688214302063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 3.9337221533060074e-03</internalNodes>
          <leafValues>
            -3.3288389444351196e-01 5.2361363172531128e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 -1.5044892206788063e-02</internalNodes>
          <leafValues>
            5.5565774440765381e-01 -2.2505992650985718e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 -1.2927042320370674e-02</internalNodes>
          <leafValues>
            5.7442700862884521e-01 -1.9708566367626190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 5.5960696190595627e-03</internalNodes>
          <leafValues>
            -3.0430641770362854e-01 4.0241482853889465e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 1.5758406370878220e-02</internalNodes>
          <leafValues>
            -1.9767063856124878e-01 4.5033392310142517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 2.4262722581624985e-02</internalNodes>
          <leafValues>
            -1.6931040585041046e-01 5.9707510471343994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -3.5242564976215363e-02</internalNodes>
          <leafValues>
            6.5973556041717529e-01 -1.4519356191158295e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 2.6568008586764336e-02</internalNodes>
          <leafValues>
            -1.3476610183715820e-01 5.4296624660491943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 4.7154121100902557e-02</internalNodes>
          <leafValues>
            -1.7337851226329803e-01 4.6071702241897583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -5.3081759251654148e-03</internalNodes>
          <leafValues>
            5.4976856708526611e-01 -1.1913410574197769e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 5.3415738046169281e-02</internalNodes>
          <leafValues>
            -1.2382411211729050e-01 6.3972741365432739e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 -3.0798995867371559e-03</internalNodes>
          <leafValues>
            -8.2048600912094116e-01 1.0249497741460800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 -2.3766520898789167e-03</internalNodes>
          <leafValues>
            -7.0665025711059570e-01 6.7025005817413330e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 1.1965663870796561e-03</internalNodes>
          <leafValues>
            -2.4753804504871368e-01 3.0198124051094055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 -4.2106406763195992e-03</internalNodes>
          <leafValues>
            3.8455343246459961e-01 -1.8334107100963593e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.4618960618972778e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 725 1.0133055038750172e-02</internalNodes>
          <leafValues>
            -2.8207325935363770e-01 6.2703561782836914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 3.8468956947326660e-02</internalNodes>
          <leafValues>
            -1.4483113586902618e-01 7.4971008300781250e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -3.7523733917623758e-03</internalNodes>
          <leafValues>
            4.2959973216056824e-01 -2.1445912122726440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 9.9978316575288773e-04</internalNodes>
          <leafValues>
            -1.9259409606456757e-01 4.2325544357299805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 -1.6786376014351845e-02</internalNodes>
          <leafValues>
            5.0582861900329590e-01 -1.8607729673385620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 3.0330579727888107e-02</internalNodes>
          <leafValues>
            -2.1100421249866486e-01 4.2819553613662720e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 1.5150709077715874e-02</internalNodes>
          <leafValues>
            -2.1129198372364044e-01 3.6263525485992432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 -3.6349350120872259e-03</internalNodes>
          <leafValues>
            3.9500275254249573e-01 -1.8650630116462708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 -7.2061517275869846e-03</internalNodes>
          <leafValues>
            -7.2816300392150879e-01 1.1153221875429153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 -2.0212728530168533e-02</internalNodes>
          <leafValues>
            5.6296736001968384e-01 -1.2056054919958115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 2.5640423409640789e-03</internalNodes>
          <leafValues>
            -2.3753854632377625e-01 3.5794413089752197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 -6.2726587057113647e-03</internalNodes>
          <leafValues>
            -6.7750877141952515e-01 1.2570948898792267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 7.8710336238145828e-03</internalNodes>
          <leafValues>
            6.9211356341838837e-02 -7.6449161767959595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 5.9134580194950104e-02</internalNodes>
          <leafValues>
            -1.7324967682361603e-01 3.3361187577247620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 -2.8770491480827332e-03</internalNodes>
          <leafValues>
            3.6101511120796204e-01 -1.6122241318225861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 -5.7046953588724136e-03</internalNodes>
          <leafValues>
            -6.7659336328506470e-01 8.4153175354003906e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -7.8070178627967834e-02</internalNodes>
          <leafValues>
            6.0763663053512573e-01 -1.1037797480821609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 6.5858578309416771e-03</internalNodes>
          <leafValues>
            9.3060031533241272e-02 -7.0068693161010742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 -2.0920131355524063e-03</internalNodes>
          <leafValues>
            2.8173315525054932e-01 -1.8406434357166290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 -2.1252598613500595e-02</internalNodes>
          <leafValues>
            3.9672371745109558e-01 -1.5127600729465485e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 -3.2937981188297272e-02</internalNodes>
          <leafValues>
            3.9487251639366150e-01 -1.3228580355644226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1016 4.9491915851831436e-03</internalNodes>
          <leafValues>
            1.1234261840581894e-01 -4.7414371371269226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 3.4271054901182652e-03</internalNodes>
          <leafValues>
            7.8623600304126740e-02 -5.7828009128570557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 -6.0859560035169125e-03</internalNodes>
          <leafValues>
            -5.0091904401779175e-01 9.1926425695419312e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 990 1.2116413563489914e-02</internalNodes>
          <leafValues>
            -1.7154470086097717e-01 2.6759135723114014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 456 8.2814376801252365e-03</internalNodes>
          <leafValues>
            -1.2938241660594940e-01 3.5665917396545410e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.4103703498840332e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 532 -1.0988018475472927e-02</internalNodes>
          <leafValues>
            6.4358645677566528e-01 -2.3149165511131287e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 -7.8163212165236473e-03</internalNodes>
          <leafValues>
            5.4850798845291138e-01 -1.7881108820438385e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 7.1337133646011353e-02</internalNodes>
          <leafValues>
            -1.7631703615188599e-01 4.5873588323593140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 5.2656695246696472e-02</internalNodes>
          <leafValues>
            -1.3836050033569336e-01 5.6253266334533691e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 1.5166129916906357e-02</internalNodes>
          <leafValues>
            -2.0990008115768433e-01 4.0483391284942627e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 -1.4538960531353951e-03</internalNodes>
          <leafValues>
            3.3692672848701477e-01 -2.1745139360427856e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 1.1136244982481003e-02</internalNodes>
          <leafValues>
            -1.5003634989261627e-01 5.2208083868026733e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -3.3187635708600283e-03</internalNodes>
          <leafValues>
            3.9145255088806152e-01 -1.9418042898178101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 4.9791105091571808e-02</internalNodes>
          <leafValues>
            -1.0192432254552841e-01 5.4612094163894653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 4.3476112186908722e-02</internalNodes>
          <leafValues>
            -1.2768918275833130e-01 5.0825607776641846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 -2.8149634599685669e-03</internalNodes>
          <leafValues>
            -7.0453292131423950e-01 1.2536850571632385e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 1.6101204091683030e-03</internalNodes>
          <leafValues>
            -2.6965174078941345e-01 2.2737979888916016e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -1.5866891480982304e-03</internalNodes>
          <leafValues>
            -6.6891485452651978e-01 1.1686278134584427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -3.2338392920792103e-03</internalNodes>
          <leafValues>
            -6.7284232378005981e-01 6.6228114068508148e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 -9.9909156560897827e-03</internalNodes>
          <leafValues>
            3.6961549520492554e-01 -1.5993835031986237e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 4.8409838229417801e-02</internalNodes>
          <leafValues>
            -1.0068884491920471e-01 5.0648134946823120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 8.0585200339555740e-03</internalNodes>
          <leafValues>
            -1.6782654821872711e-01 3.5382467508316040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 -1.1718695983290672e-02</internalNodes>
          <leafValues>
            4.3832498788833618e-01 -1.2780784070491791e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 5.7147610932588577e-03</internalNodes>
          <leafValues>
            7.5814604759216309e-02 -7.2597140073776245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 -2.0917234942317009e-03</internalNodes>
          <leafValues>
            -6.0916984081268311e-01 8.4811411798000336e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 5.7651996612548828e-03</internalNodes>
          <leafValues>
            -1.9243443012237549e-01 2.8976503014564514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 -2.8093710541725159e-02</internalNodes>
          <leafValues>
            5.4229170083999634e-01 -1.0005526244640350e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 8.9291334152221680e-03</internalNodes>
          <leafValues>
            8.3808921277523041e-02 -6.3219338655471802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 -5.1958961412310600e-03</internalNodes>
          <leafValues>
            -5.4964137077331543e-01 7.9588212072849274e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 9.2318728566169739e-03</internalNodes>
          <leafValues>
            -1.2818163633346558e-01 4.2056322097778320e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 -2.0556427538394928e-02</internalNodes>
          <leafValues>
            3.2048463821411133e-01 -1.3858842849731445e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>35</maxWeakCount>
      <stageThreshold>-1.4265209436416626e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 683 1.8821602687239647e-02</internalNodes>
          <leafValues>
            -1.7807419598102570e-01 5.9040957689285278e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 -9.5066539943218231e-03</internalNodes>
          <leafValues>
            5.0587177276611328e-01 -1.7767964303493500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 1.3296608813107014e-03</internalNodes>
          <leafValues>
            -1.6886346042156219e-01 3.6326614022254944e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 3.5266026854515076e-02</internalNodes>
          <leafValues>
            -1.1824090778827667e-01 5.8951085805892944e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 1.7804209142923355e-02</internalNodes>
          <leafValues>
            -1.4211210608482361e-01 5.1762068271636963e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1001 4.7029324923641980e-04</internalNodes>
          <leafValues>
            -2.4296821653842926e-01 2.5087893009185791e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 7.1838246658444405e-03</internalNodes>
          <leafValues>
            9.2609666287899017e-02 -6.7694115638732910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -5.7565318420529366e-03</internalNodes>
          <leafValues>
            -7.3053181171417236e-01 8.2794629037380219e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 2.0850602537393570e-02</internalNodes>
          <leafValues>
            -1.7353208363056183e-01 3.3287450671195984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 3.1848326325416565e-03</internalNodes>
          <leafValues>
            -2.0941653847694397e-01 2.6059800386428833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 -7.5752258300781250e-02</internalNodes>
          <leafValues>
            5.1588213443756104e-01 -1.0057342052459717e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 2.8725115582346916e-02</internalNodes>
          <leafValues>
            -1.5012685954570770e-01 4.1436919569969177e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 -1.7325732856988907e-02</internalNodes>
          <leafValues>
            3.8678762316703796e-01 -1.3586300611495972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -3.2187681645154953e-03</internalNodes>
          <leafValues>
            -5.1590150594711304e-01 1.1511231958866119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1020 -6.1595086008310318e-03</internalNodes>
          <leafValues>
            -7.0271849632263184e-01 5.5648274719715118e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 -8.7264683097600937e-03</internalNodes>
          <leafValues>
            2.6393634080886841e-01 -1.8446569144725800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 8.1868227571249008e-03</internalNodes>
          <leafValues>
            8.0838531255722046e-02 -5.5512112379074097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -7.8468751162290573e-03</internalNodes>
          <leafValues>
            -5.7306796312332153e-01 8.3454042673110962e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 2.9962153639644384e-03</internalNodes>
          <leafValues>
            6.2645487487316132e-02 -5.8123600482940674e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 -4.3795984238386154e-03</internalNodes>
          <leafValues>
            2.2211562097072601e-01 -1.9649308919906616e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 -6.3172029331326485e-03</internalNodes>
          <leafValues>
            -6.6067039966583252e-01 6.4884319901466370e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 1.3302030274644494e-03</internalNodes>
          <leafValues>
            -1.0496762394905090e-01 4.2326071858406067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 -4.3333107605576515e-03</internalNodes>
          <leafValues>
            -4.9972066283226013e-01 8.7225496768951416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 -3.5346355289220810e-03</internalNodes>
          <leafValues>
            3.0818134546279907e-01 -1.4765550196170807e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 -8.7353587150573730e-03</internalNodes>
          <leafValues>
            -6.5214675664901733e-01 7.1881487965583801e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 -1.5620354562997818e-02</internalNodes>
          <leafValues>
            3.5721915960311890e-01 -1.1427627503871918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 -3.9745438843965530e-03</internalNodes>
          <leafValues>
            -6.6090464591979980e-01 6.2067609280347824e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 -6.7040426656603813e-03</internalNodes>
          <leafValues>
            2.7337384223937988e-01 -1.4059108495712280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 3.5359347239136696e-03</internalNodes>
          <leafValues>
            6.1201948672533035e-02 -6.0017114877700806e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 6.0818484053015709e-03</internalNodes>
          <leafValues>
            -1.5247075259685516e-01 2.4383027851581573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 -7.2771648410707712e-04</internalNodes>
          <leafValues>
            3.0065426230430603e-01 -1.2037902325391769e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 4.6168416738510132e-03</internalNodes>
          <leafValues>
            5.5311698466539383e-02 -7.5343269109725952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 2.5280299596488476e-03</internalNodes>
          <leafValues>
            5.7204965502023697e-02 -5.3993463516235352e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 1.5074670314788818e-02</internalNodes>
          <leafValues>
            -9.6106290817260742e-02 3.9084190130233765e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 -8.4932018071413040e-03</internalNodes>
          <leafValues>
            3.4130987524986267e-01 -1.4117397367954254e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>37</maxWeakCount>
      <stageThreshold>-1.3977209329605103e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 794 -2.5338861159980297e-03</internalNodes>
          <leafValues>
            5.7321399450302124e-01 -2.0396080613136292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 -6.5112011507153511e-03</internalNodes>
          <leafValues>
            3.7378740310668945e-01 -2.5049039721488953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 1.6318978741765022e-03</internalNodes>
          <leafValues>
            -2.1858637034893036e-01 3.5027471184730530e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 3.3452022820711136e-02</internalNodes>
          <leafValues>
            -1.4827065169811249e-01 4.7324529290199280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 -1.1114047840237617e-02</internalNodes>
          <leafValues>
            4.1662359237670898e-01 -2.1660456061363220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 -1.2996498262509704e-03</internalNodes>
          <leafValues>
            4.7613915801048279e-01 -1.6742442548274994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -3.2986078877002001e-03</internalNodes>
          <leafValues>
            -6.7662662267684937e-01 8.6653761565685272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 6.6831205040216446e-03</internalNodes>
          <leafValues>
            -2.0158858597278595e-01 2.6189696788787842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 2.1282089874148369e-03</internalNodes>
          <leafValues>
            -1.1156299710273743e-01 4.0097075700759888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 -9.0472139418125153e-03</internalNodes>
          <leafValues>
            3.2078295946121216e-01 -1.6775439679622650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 -5.3160609677433968e-03</internalNodes>
          <leafValues>
            -5.5567348003387451e-01 1.2950280308723450e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 7.9724024981260300e-03</internalNodes>
          <leafValues>
            -2.1466700732707977e-01 2.2514854371547699e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 -2.1980279125273228e-03</internalNodes>
          <leafValues>
            2.8711742162704468e-01 -1.6561916470527649e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 5.3897619247436523e-02</internalNodes>
          <leafValues>
            -1.4823001623153687e-01 3.4951418638229370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -7.6241128146648407e-02</internalNodes>
          <leafValues>
            6.0101884603500366e-01 -8.8328786194324493e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -8.3202747628092766e-03</internalNodes>
          <leafValues>
            -7.2828358411788940e-01 8.7956465780735016e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 5.3778752684593201e-02</internalNodes>
          <leafValues>
            -1.0316975414752960e-01 5.0247919559478760e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 -1.2401826679706573e-02</internalNodes>
          <leafValues>
            2.7538898587226868e-01 -1.5569972991943359e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 986 1.3729928061366081e-02</internalNodes>
          <leafValues>
            -1.3373774290084839e-01 3.0739122629165649e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 -2.2788168862462044e-03</internalNodes>
          <leafValues>
            2.2555501759052277e-01 -1.9497908651828766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 3.6288173869252205e-03</internalNodes>
          <leafValues>
            4.8981692641973495e-02 -7.9248648881912231e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 5.2453137934207916e-02</internalNodes>
          <leafValues>
            -1.3389803469181061e-01 3.2700663805007935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 3.1685843132436275e-03</internalNodes>
          <leafValues>
            -1.4415425062179565e-01 2.8044179081916809e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 8.9051481336355209e-03</internalNodes>
          <leafValues>
            6.1227656900882721e-02 -7.0277702808380127e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -1.3966157566756010e-03</internalNodes>
          <leafValues>
            4.2409667372703552e-01 -1.0888981819152832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 -6.7695947363972664e-03</internalNodes>
          <leafValues>
            -5.1588076353073120e-01 8.3254821598529816e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 2.2157761268317699e-03</internalNodes>
          <leafValues>
            -1.3696527481079102e-01 2.8638482093811035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 2.7808796148747206e-03</internalNodes>
          <leafValues>
            7.1316704154014587e-02 -6.0322999954223633e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 4.5836241915822029e-03</internalNodes>
          <leafValues>
            -1.2486589699983597e-01 3.2929363846778870e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1042 -5.1459800451993942e-03</internalNodes>
          <leafValues>
            -5.3781992197036743e-01 7.6631128787994385e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1043 2.4449056945741177e-03</internalNodes>
          <leafValues>
            8.5920669138431549e-02 -4.0670683979988098e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -2.7756379917263985e-02</internalNodes>
          <leafValues>
            3.7449231743812561e-01 -1.0538945347070694e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 -1.8243372440338135e-02</internalNodes>
          <leafValues>
            3.4281516075134277e-01 -9.9502928555011749e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 3.8416781462728977e-03</internalNodes>
          <leafValues>
            7.3987491428852081e-02 -4.8903524875640869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -1.2322908267378807e-02</internalNodes>
          <leafValues>
            2.1036790311336517e-01 -1.5852701663970947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 -4.1760304011404514e-03</internalNodes>
          <leafValues>
            3.1288132071495056e-01 -1.1697492748498917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 -2.8026863932609558e-02</internalNodes>
          <leafValues>
            3.3711743354797363e-01 -1.2294299900531769e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>42</maxWeakCount>
      <stageThreshold>-1.3775455951690674e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 725 1.3382414355874062e-02</internalNodes>
          <leafValues>
            -1.7922241985797882e-01 5.0368404388427734e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 1.9935802556574345e-03</internalNodes>
          <leafValues>
            -2.5249919295310974e-01 3.5295018553733826e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -1.3569685397669673e-03</internalNodes>
          <leafValues>
            4.1222429275512695e-01 -1.8140394985675812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 2.5418698787689209e-03</internalNodes>
          <leafValues>
            -2.3195247352123260e-01 2.5945317745208740e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 1.1867792345583439e-03</internalNodes>
          <leafValues>
            -1.1509010195732117e-01 4.0095508098602295e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -4.0491363033652306e-03</internalNodes>
          <leafValues>
            -7.6275551319122314e-01 8.0663219094276428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 2.4698153138160706e-02</internalNodes>
          <leafValues>
            -9.9053405225276947e-02 4.6469488739967346e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 1.3041709549725056e-02</internalNodes>
          <leafValues>
            -1.3049817085266113e-01 4.7066822648048401e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 -2.0927201956510544e-02</internalNodes>
          <leafValues>
            -7.2363191843032837e-01 7.5520738959312439e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 1.6108792275190353e-02</internalNodes>
          <leafValues>
            8.9385204017162323e-02 -5.0678378343582153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 -8.6308103054761887e-03</internalNodes>
          <leafValues>
            3.1878158450126648e-01 -1.3526505231857300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 1.2651814613491297e-03</internalNodes>
          <leafValues>
            -1.2344279885292053e-01 4.0271109342575073e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -3.0170590616762638e-03</internalNodes>
          <leafValues>
            -5.6960099935531616e-01 7.0437252521514893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 -3.5529488231986761e-03</internalNodes>
          <leafValues>
            2.0624065399169922e-01 -1.8426756560802460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 2.8021419420838356e-03</internalNodes>
          <leafValues>
            7.2748780250549316e-02 -5.3796368837356567e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 -9.9331419914960861e-04</internalNodes>
          <leafValues>
            2.4827398359775543e-01 -1.5866567194461823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 -7.1950745768845081e-03</internalNodes>
          <leafValues>
            -5.0943744182586670e-01 7.3041573166847229e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 -8.7737981230020523e-03</internalNodes>
          <leafValues>
            2.4838714301586151e-01 -1.5162147581577301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 5.6750684976577759e-02</internalNodes>
          <leafValues>
            -8.4416143596172333e-02 4.4269657135009766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 1.8110256642103195e-03</internalNodes>
          <leafValues>
            -1.7787678539752960e-01 2.2753682732582092e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 6.1733853071928024e-02</internalNodes>
          <leafValues>
            -1.4452947676181793e-01 2.6785543560981750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 1.7999792471528053e-03</internalNodes>
          <leafValues>
            5.3869031369686127e-02 -7.0216673612594604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 -1.7839821521192789e-03</internalNodes>
          <leafValues>
            -7.3474282026290894e-01 4.3809492141008377e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -2.2269869223237038e-03</internalNodes>
          <leafValues>
            2.5256577134132385e-01 -1.4765015244483948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 7.7408831566572189e-04</internalNodes>
          <leafValues>
            -1.6781617701053619e-01 2.5267890095710754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 9.6316616982221603e-03</internalNodes>
          <leafValues>
            5.8525908738374710e-02 -6.3684886693954468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 -1.1892126873135567e-02</internalNodes>
          <leafValues>
            2.6363542675971985e-01 -1.4106634259223938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 4.8407237976789474e-02</internalNodes>
          <leafValues>
            -1.0837136209011078e-01 3.6018091440200806e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 572 -1.0315750539302826e-01</internalNodes>
          <leafValues>
            -7.3309695720672607e-01 6.4976803958415985e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 -2.6544972788542509e-03</internalNodes>
          <leafValues>
            2.7709859609603882e-01 -1.3764445483684540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1033 -4.8850756138563156e-03</internalNodes>
          <leafValues>
            -5.0026285648345947e-01 6.8797707557678223e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 -1.1310833506286144e-02</internalNodes>
          <leafValues>
            2.5653550028800964e-01 -1.3755545020103455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -3.8394361734390259e-02</internalNodes>
          <leafValues>
            2.6404461264610291e-01 -1.3614650070667267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 5.8298893272876740e-03</internalNodes>
          <leafValues>
            6.0382172465324402e-02 -5.9578329324722290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 2.2631133906543255e-03</internalNodes>
          <leafValues>
            -1.0302778333425522e-01 3.4782779216766357e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 -1.8709234893321991e-02</internalNodes>
          <leafValues>
            -7.6758313179016113e-01 4.6181913465261459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 3.7359733134508133e-02</internalNodes>
          <leafValues>
            -1.3407541811466217e-01 2.5607112050056458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 -5.3099328652024269e-03</internalNodes>
          <leafValues>
            -6.9016355276107788e-01 4.7683756798505783e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 -1.5396323287859559e-03</internalNodes>
          <leafValues>
            3.7874689698219299e-01 -9.2663109302520752e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 -2.6333518326282501e-03</internalNodes>
          <leafValues>
            2.9358446598052979e-01 -1.2460695207118988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 1.6515964642167091e-02</internalNodes>
          <leafValues>
            -1.4082725346088409e-01 2.3664724826812744e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 -4.4658156111836433e-03</internalNodes>
          <leafValues>
            -5.9253305196762085e-01 5.5994171649217606e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>50</maxWeakCount>
      <stageThreshold>-1.3835698366165161e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 898 1.5156399458646774e-03</internalNodes>
          <leafValues>
            -1.0024535655975342e-01 5.8807808160781860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 -3.5168868489563465e-03</internalNodes>
          <leafValues>
            4.0972998738288879e-01 -1.6088742017745972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 2.3035616613924503e-03</internalNodes>
          <leafValues>
            -1.8985269963741302e-01 2.9883998632431030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 4.5840561389923096e-02</internalNodes>
          <leafValues>
            -1.4383240044116974e-01 4.7528687119483948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 5.5156396701931953e-03</internalNodes>
          <leafValues>
            -1.7356806993484497e-01 3.4583050012588501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 3.9731184951961040e-03</internalNodes>
          <leafValues>
            7.8886620700359344e-02 -5.6442558765411377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 -5.6995991617441177e-03</internalNodes>
          <leafValues>
            -4.7576662898063660e-01 9.4875656068325043e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 -9.6501735970377922e-03</internalNodes>
          <leafValues>
            2.3381656408309937e-01 -1.8310526013374329e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 6.1656545847654343e-02</internalNodes>
          <leafValues>
            -1.4697165787220001e-01 3.6247691512107849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 1.1418928205966949e-01</internalNodes>
          <leafValues>
            -8.8033527135848999e-02 4.4633501768112183e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 -1.1903396807610989e-02</internalNodes>
          <leafValues>
            3.3496665954589844e-01 -1.2121009081602097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 -4.1371315717697144e-02</internalNodes>
          <leafValues>
            4.1400006413459778e-01 -9.7229279577732086e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 7.8342631459236145e-03</internalNodes>
          <leafValues>
            -1.6631671786308289e-01 2.5738984346389771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 -4.5139621943235397e-03</internalNodes>
          <leafValues>
            -4.6883803606033325e-01 8.7662570178508759e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 1.5914421528577805e-03</internalNodes>
          <leafValues>
            -1.1636006087064743e-01 3.2739594578742981e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 -5.2607608959078789e-03</internalNodes>
          <leafValues>
            -6.7755740880966187e-01 5.1752120256423950e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 3.1824512407183647e-03</internalNodes>
          <leafValues>
            5.2379645407199860e-02 -6.0918039083480835e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 -3.6813789047300816e-03</internalNodes>
          <leafValues>
            4.8251116275787354e-01 -9.2318780720233917e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 -4.3226117268204689e-03</internalNodes>
          <leafValues>
            -5.7561415433883667e-01 5.9672243893146515e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 -7.1843853220343590e-03</internalNodes>
          <leafValues>
            2.6631006598472595e-01 -1.4015418291091919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 2.1028071641921997e-03</internalNodes>
          <leafValues>
            -1.1286304146051407e-01 3.5946926474571228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 8.5248583927750587e-03</internalNodes>
          <leafValues>
            6.9424033164978027e-02 -5.2462881803512573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 6.9785099476575851e-03</internalNodes>
          <leafValues>
            5.6668873876333237e-02 -5.6192052364349365e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 -5.2639590576291084e-03</internalNodes>
          <leafValues>
            -5.8648955821990967e-01 5.0352573394775391e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 2.8417459689080715e-03</internalNodes>
          <leafValues>
            -1.3425759971141815e-01 2.7325555682182312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 -1.3187457807362080e-02</internalNodes>
          <leafValues>
            4.0453648567199707e-01 -9.1843754053115845e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 -6.7344801500439644e-03</internalNodes>
          <leafValues>
            -7.5647395849227905e-01 5.0157479941844940e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 2.1363141015172005e-02</internalNodes>
          <leafValues>
            4.7982390969991684e-02 -5.5388218164443970e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 1.6145884292200208e-03</internalNodes>
          <leafValues>
            7.9808227717876434e-02 -3.7233716249465942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 -2.2595757618546486e-03</internalNodes>
          <leafValues>
            2.8343635797500610e-01 -1.1216876655817032e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 1.4407988637685776e-02</internalNodes>
          <leafValues>
            -1.0392460227012634e-01 3.1299999356269836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 -1.4912552433088422e-03</internalNodes>
          <leafValues>
            2.8538599610328674e-01 -1.0644508898258209e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 9.8895151168107986e-03</internalNodes>
          <leafValues>
            5.0090074539184570e-02 -6.2053185701370239e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 4.2754956521093845e-03</internalNodes>
          <leafValues>
            6.5051443874835968e-02 -4.2582303285598755e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 -2.5489409454166889e-03</internalNodes>
          <leafValues>
            3.1278640031814575e-01 -9.9601686000823975e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 -6.0358326882123947e-03</internalNodes>
          <leafValues>
            2.2685267031192780e-01 -1.3849361240863800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 1.1879121884703636e-02</internalNodes>
          <leafValues>
            -8.9687183499336243e-02 3.7642294168472290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 1.2982923537492752e-02</internalNodes>
          <leafValues>
            4.3990727514028549e-02 -7.3371982574462891e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 993 -2.8599319048225880e-03</internalNodes>
          <leafValues>
            -4.3102917075157166e-01 5.9561621397733688e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 -3.5829999251291156e-04</internalNodes>
          <leafValues>
            1.7152757942676544e-01 -1.6511310636997223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 2.5972571223974228e-02</internalNodes>
          <leafValues>
            -1.2855969369411469e-01 2.2820757329463959e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 4.2565623298287392e-03</internalNodes>
          <leafValues>
            5.7662181556224823e-02 -5.3734982013702393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -2.9159568250179291e-02</internalNodes>
          <leafValues>
            -6.3020753860473633e-01 4.0746636688709259e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 3.1341956928372383e-03</internalNodes>
          <leafValues>
            -8.1374719738960266e-02 4.1371321678161621e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 -1.3592604082077742e-03</internalNodes>
          <leafValues>
            3.2382342219352722e-01 -9.7880341112613678e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 -6.9904811680316925e-03</internalNodes>
          <leafValues>
            -6.8850576877593994e-01 4.2428225278854370e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 -8.7879784405231476e-03</internalNodes>
          <leafValues>
            -5.8945190906524658e-01 3.7613209336996078e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 -1.7947785556316376e-02</internalNodes>
          <leafValues>
            3.1659606099128723e-01 -8.7437197566032410e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 8.0379713326692581e-03</internalNodes>
          <leafValues>
            -1.1311284452676773e-01 3.0860018730163574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 3.0642822384834290e-03</internalNodes>
          <leafValues>
            4.8351831734180450e-02 -6.0563534498214722e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>54</maxWeakCount>
      <stageThreshold>-1.3756012916564941e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 798 -1.7431776504963636e-03</internalNodes>
          <leafValues>
            5.5538344383239746e-01 -1.0357239097356796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 4.4551412574946880e-03</internalNodes>
          <leafValues>
            -1.2460361421108246e-01 5.1942145824432373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 3.5308140795677900e-03</internalNodes>
          <leafValues>
            -2.2974169254302979e-01 2.7043044567108154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 -1.5887852758169174e-02</internalNodes>
          <leafValues>
            4.1745069622993469e-01 -1.1281611770391464e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 1.1611310765147209e-02</internalNodes>
          <leafValues>
            -1.9416445493698120e-01 2.5554594397544861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 1.5740045346319675e-03</internalNodes>
          <leafValues>
            -1.2263108044862747e-01 3.8852572441101074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 5.1882643252611160e-02</internalNodes>
          <leafValues>
            -7.5461924076080322e-02 5.0257563591003418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 -3.8624972105026245e-02</internalNodes>
          <leafValues>
            4.0001305937767029e-01 -9.6231088042259216e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 -3.9408572018146515e-02</internalNodes>
          <leafValues>
            3.0533725023269653e-01 -1.6677139699459076e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 7.5884531252086163e-03</internalNodes>
          <leafValues>
            9.8107770085334778e-02 -5.8249044418334961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 7.2114326059818268e-02</internalNodes>
          <leafValues>
            -1.4419755339622498e-01 2.8208708763122559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 5.5582458153367043e-03</internalNodes>
          <leafValues>
            7.2843901813030243e-02 -5.5255079269409180e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 -4.7345291823148727e-03</internalNodes>
          <leafValues>
            3.3209753036499023e-01 -1.2499606609344482e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 5.1413839682936668e-03</internalNodes>
          <leafValues>
            6.4787313342094421e-02 -6.4880597591400146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 999 5.4608630016446114e-03</internalNodes>
          <leafValues>
            3.7491828203201294e-02 -7.5315922498703003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 -8.6404485045932233e-05</internalNodes>
          <leafValues>
            1.7464619874954224e-01 -1.8258170783519745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 442 6.1132330447435379e-03</internalNodes>
          <leafValues>
            7.5624085962772369e-02 -4.3711006641387939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 -7.0670098066329956e-03</internalNodes>
          <leafValues>
            2.1796958148479462e-01 -1.4547325670719147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 9.4080460257828236e-04</internalNodes>
          <leafValues>
            -1.2536728382110596e-01 2.8143358230590820e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 -2.6800869964063168e-03</internalNodes>
          <leafValues>
            -4.2977494001388550e-01 8.2963027060031891e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 5.8945640921592712e-03</internalNodes>
          <leafValues>
            4.2834181338548660e-02 -6.0937494039535522e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 1.0121082887053490e-03</internalNodes>
          <leafValues>
            -1.1036285758018494e-01 2.9971688985824585e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 3.1157936900854111e-03</internalNodes>
          <leafValues>
            7.3115289211273193e-02 -4.3226471543312073e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -3.3052214421331882e-03</internalNodes>
          <leafValues>
            -4.9826300144195557e-01 5.1225960254669189e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 8.3188470453023911e-03</internalNodes>
          <leafValues>
            5.0362452864646912e-02 -4.8688000440597534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -2.5094528682529926e-03</internalNodes>
          <leafValues>
            2.6902040839195251e-01 -1.0433372855186462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 1.1217880528420210e-03</internalNodes>
          <leafValues>
            -1.1188100278377533e-01 3.1254816055297852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 -2.9259414877742529e-03</internalNodes>
          <leafValues>
            -5.7495939731597900e-01 5.3564101457595825e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 -1.1687271296977997e-02</internalNodes>
          <leafValues>
            2.5880128145217896e-01 -1.0639669001102448e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 3.5054073669016361e-03</internalNodes>
          <leafValues>
            5.4045904427766800e-02 -5.5625277757644653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 1.9068794324994087e-02</internalNodes>
          <leafValues>
            -1.1246301978826523e-01 2.5745245814323425e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 4.6145436353981495e-03</internalNodes>
          <leafValues>
            6.7216314375400543e-02 -4.1385611891746521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 -8.2267355173826218e-03</internalNodes>
          <leafValues>
            2.1265375614166260e-01 -1.3443692028522491e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 -1.4355888590216637e-02</internalNodes>
          <leafValues>
            2.5618723034858704e-01 -1.0785522311925888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 8.0431215465068817e-03</internalNodes>
          <leafValues>
            -1.4258129894733429e-01 2.2692860662937164e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 -5.6914249435067177e-03</internalNodes>
          <leafValues>
            -4.8886317014694214e-01 6.0331270098686218e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -2.5912215933203697e-03</internalNodes>
          <leafValues>
            2.1062785387039185e-01 -1.4967896044254303e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 5.5204275995492935e-03</internalNodes>
          <leafValues>
            -8.1333734095096588e-02 3.8316065073013306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 5.3790090605616570e-03</internalNodes>
          <leafValues>
            -9.3129634857177734e-02 3.2883483171463013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 -7.2196200489997864e-03</internalNodes>
          <leafValues>
            -6.6427856683731079e-01 4.4702950865030289e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 -8.3873540163040161e-02</internalNodes>
          <leafValues>
            -7.9910254478454590e-01 2.7107261121273041e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 -3.4268260933458805e-03</internalNodes>
          <leafValues>
            2.5298807024955750e-01 -1.0898132622241974e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 -3.7466005887836218e-03</internalNodes>
          <leafValues>
            -5.5346089601516724e-01 5.2094604820013046e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 1.2452949304133654e-03</internalNodes>
          <leafValues>
            -8.2017965614795685e-02 3.5483068227767944e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1013 -6.2445802614092827e-03</internalNodes>
          <leafValues>
            -5.0969594717025757e-01 5.4533429443836212e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -1.1970927007496357e-03</internalNodes>
          <leafValues>
            3.6470764875411987e-01 -7.7394872903823853e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 3.0796977225691080e-03</internalNodes>
          <leafValues>
            5.3208738565444946e-02 -5.0689512491226196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -3.9015077054500580e-02</internalNodes>
          <leafValues>
            1.9598089158535004e-01 -1.3218660652637482e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 -7.7085788361728191e-03</internalNodes>
          <leafValues>
            2.2754703462123871e-01 -1.2544488906860352e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 3.2509677112102509e-02</internalNodes>
          <leafValues>
            -6.7099742591381073e-02 4.1469818353652954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 3.0232844874262810e-03</internalNodes>
          <leafValues>
            6.6373795270919800e-02 -4.2127549648284912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 2.5392756797373295e-03</internalNodes>
          <leafValues>
            -1.1576391756534576e-01 2.3464009165763855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1013 6.8497275933623314e-03</internalNodes>
          <leafValues>
            4.5596633106470108e-02 -5.8435302972793579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 -4.4358119368553162e-02</internalNodes>
          <leafValues>
            -3.9718165993690491e-01 6.2707424163818359e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>63</maxWeakCount>
      <stageThreshold>-1.4057025909423828e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 804 5.0806580111384392e-03</internalNodes>
          <leafValues>
            -7.9617008566856384e-02 5.6362086534500122e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 2.0602284930646420e-03</internalNodes>
          <leafValues>
            -1.8717131018638611e-01 3.4062680602073669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 6.1347078531980515e-02</internalNodes>
          <leafValues>
            -1.3253036141395569e-01 4.0938606858253479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -6.0383215546607971e-02</internalNodes>
          <leafValues>
            4.1172346472740173e-01 -1.4447186887264252e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -3.0238348990678787e-03</internalNodes>
          <leafValues>
            3.4262558817863464e-01 -1.0982885956764221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 458 4.0474245324730873e-03</internalNodes>
          <leafValues>
            7.1186766028404236e-02 -5.0650447607040405e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 -2.0359824411571026e-03</internalNodes>
          <leafValues>
            2.2166600823402405e-01 -1.6060648858547211e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 2.7303429305902682e-05</internalNodes>
          <leafValues>
            -2.6211214065551758e-01 1.2801185250282288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 1.2323079630732536e-02</internalNodes>
          <leafValues>
            8.2502633333206177e-02 -4.5231887698173523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 2.2477287799119949e-02</internalNodes>
          <leafValues>
            -7.7229477465152740e-02 4.5144733786582947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -1.4673802070319653e-02</internalNodes>
          <leafValues>
            3.5660189390182495e-01 -1.1584777384996414e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 9.9029816687107086e-02</internalNodes>
          <leafValues>
            -1.6957059502601624e-01 2.2625257074832916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 -1.0632930323481560e-02</internalNodes>
          <leafValues>
            -5.6829780340194702e-01 7.1929946541786194e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 2.5341216474771500e-02</internalNodes>
          <leafValues>
            -1.2931844592094421e-01 2.6161769032478333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 5.8172484859824181e-03</internalNodes>
          <leafValues>
            -1.5375703573226929e-01 2.0636843144893646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -2.0786169171333313e-01</internalNodes>
          <leafValues>
            3.9931070804595947e-01 -7.7051497995853424e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 2.2137831151485443e-01</internalNodes>
          <leafValues>
            -7.2486869990825653e-02 3.9756566286087036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 3.4148676786571741e-04</internalNodes>
          <leafValues>
            -1.5928100049495697e-01 1.8005076050758362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 -6.7202709615230560e-03</internalNodes>
          <leafValues>
            -6.7838191986083984e-01 4.5886330306529999e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 1.4110710471868515e-03</internalNodes>
          <leafValues>
            -9.7257830202579498e-02 3.2224002480506897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 4.2120069265365601e-02</internalNodes>
          <leafValues>
            -8.8405482470989227e-02 3.2538983225822449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 -1.3846142683178186e-03</internalNodes>
          <leafValues>
            2.0695628225803375e-01 -1.5275791287422180e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 817 3.5425978712737560e-03</internalNodes>
          <leafValues>
            -1.2709444761276245e-01 2.1816165745258331e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 3.3351695165038109e-03</internalNodes>
          <leafValues>
            4.8398405313491821e-02 -6.0871434211730957e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 -3.3201207406818867e-03</internalNodes>
          <leafValues>
            -4.8987022042274475e-01 5.5623263120651245e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 915 1.0103111853823066e-03</internalNodes>
          <leafValues>
            -1.5765775740146637e-01 1.6940611600875854e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 4.9717966467142105e-03</internalNodes>
          <leafValues>
            5.1272217184305191e-02 -5.4395431280136108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 1.7913591582328081e-03</internalNodes>
          <leafValues>
            -7.2745941579341888e-02 4.0087917447090149e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 -1.3228422030806541e-02</internalNodes>
          <leafValues>
            -3.5441592335700989e-01 7.9325266182422638e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 2.0421743392944336e-03</internalNodes>
          <leafValues>
            -5.9137169271707535e-02 4.6143886446952820e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -5.9784355107694864e-04</internalNodes>
          <leafValues>
            2.5433012843132019e-01 -1.0601133853197098e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 -5.1422840915620327e-03</internalNodes>
          <leafValues>
            -4.4627833366394043e-01 6.1951976269483566e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 6.4243013039231300e-03</internalNodes>
          <leafValues>
            3.1528502702713013e-02 -7.2403544187545776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1035 3.4636156633496284e-03</internalNodes>
          <leafValues>
            3.7317775189876556e-02 -5.4165351390838623e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 3.2000489532947540e-02</internalNodes>
          <leafValues>
            3.0169567093253136e-02 -7.1302002668380737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 -5.8225672692060471e-03</internalNodes>
          <leafValues>
            -4.4310861825942993e-01 4.7724053263664246e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -8.4763765335083008e-03</internalNodes>
          <leafValues>
            -6.0832363367080688e-01 3.6428902298212051e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 2.7582058683037758e-03</internalNodes>
          <leafValues>
            -1.0180406272411346e-01 2.4450653791427612e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 -3.0314538162201643e-03</internalNodes>
          <leafValues>
            -5.6130182743072510e-01 4.1730970144271851e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 3.8132141344249249e-03</internalNodes>
          <leafValues>
            4.3826375156641006e-02 -4.8639413714408875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 -1.1944114230573177e-03</internalNodes>
          <leafValues>
            1.9191412627696991e-01 -1.2599647045135498e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 -3.2212696969509125e-02</internalNodes>
          <leafValues>
            -7.3205161094665527e-01 3.3331435173749924e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 -1.0144908446818590e-03</internalNodes>
          <leafValues>
            3.0479896068572998e-01 -8.2489714026451111e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 -1.4355147257447243e-02</internalNodes>
          <leafValues>
            2.1706604957580566e-01 -1.0914804041385651e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -4.8122168518602848e-03</internalNodes>
          <leafValues>
            -6.7199075222015381e-01 4.0943562984466553e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 3.3706519752740860e-04</internalNodes>
          <leafValues>
            -1.4588885009288788e-01 1.6099508106708527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 -1.8943618983030319e-02</internalNodes>
          <leafValues>
            -5.9796541929244995e-01 3.7877634167671204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 1.5444982796907425e-02</internalNodes>
          <leafValues>
            2.6846721768379211e-02 -7.2375786304473877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 1.0463559068739414e-02</internalNodes>
          <leafValues>
            3.2184243202209473e-02 -6.0756552219390869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 2.5047133676707745e-03</internalNodes>
          <leafValues>
            -1.1925315856933594e-01 1.9379882514476776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 -1.4791900292038918e-02</internalNodes>
          <leafValues>
            1.9981779158115387e-01 -1.2553811073303223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 -6.1217732727527618e-03</internalNodes>
          <leafValues>
            -4.2455345392227173e-01 5.5959124118089676e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 563 -3.5850135609507561e-03</internalNodes>
          <leafValues>
            3.2560044527053833e-01 -7.1894593536853790e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1048 -3.2580485567450523e-03</internalNodes>
          <leafValues>
            -5.4515779018402100e-01 4.5138467103242874e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 8.5870809853076935e-03</internalNodes>
          <leafValues>
            -9.2699222266674042e-02 2.7361676096916199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 -3.5999938845634460e-03</internalNodes>
          <leafValues>
            1.7715592682361603e-01 -1.3859097659587860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 1.5299995429813862e-03</internalNodes>
          <leafValues>
            -1.0419535636901855e-01 2.1118766069412231e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 2.7578026056289673e-03</internalNodes>
          <leafValues>
            -7.0944413542747498e-02 2.9870492219924927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -6.1489176005125046e-03</internalNodes>
          <leafValues>
            -5.1581281423568726e-01 4.6433247625827789e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 8.3175086183473468e-04</internalNodes>
          <leafValues>
            -8.4185592830181122e-02 2.8132751584053040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 -6.7444925662130117e-04</internalNodes>
          <leafValues>
            2.6548036932945251e-01 -9.7815677523612976e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 -5.6643221527338028e-02</internalNodes>
          <leafValues>
            3.8170987367630005e-01 -6.2833912670612335e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 -7.5360340997576714e-03</internalNodes>
          <leafValues>
            2.2137185931205750e-01 -1.0336405038833618e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>54</maxWeakCount>
      <stageThreshold>-1.3439358472824097e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 526 -4.8420722596347332e-03</internalNodes>
          <leafValues>
            5.7400572299957275e-01 -9.5008336007595062e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 -5.9993756003677845e-03</internalNodes>
          <leafValues>
            4.5479923486709595e-01 -1.5483228862285614e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 -3.1531709246337414e-03</internalNodes>
          <leafValues>
            4.2504432797431946e-01 -1.2935030460357666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 1.2363551650196314e-03</internalNodes>
          <leafValues>
            -1.5872104465961456e-01 3.1463247537612915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -6.7780278623104095e-03</internalNodes>
          <leafValues>
            4.1302111744880676e-01 -1.7017546296119690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 1.3960017822682858e-03</internalNodes>
          <leafValues>
            -1.3419999182224274e-01 3.3868113160133362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 -3.5894233733415604e-03</internalNodes>
          <leafValues>
            3.3102113008499146e-01 -1.1498286575078964e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 5.4187951609492302e-03</internalNodes>
          <leafValues>
            -1.2790408730506897e-01 3.1275641918182373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 -3.3248444087803364e-03</internalNodes>
          <leafValues>
            -5.1654219627380371e-01 7.1216024458408356e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 7.9970825463533401e-03</internalNodes>
          <leafValues>
            6.3098005950450897e-02 -5.8896148204803467e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 6.0347835533320904e-03</internalNodes>
          <leafValues>
            6.4018696546554565e-02 -4.7639665007591248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -6.9478121586143970e-03</internalNodes>
          <leafValues>
            -6.0485291481018066e-01 7.2506561875343323e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 1.9063859945163131e-03</internalNodes>
          <leafValues>
            -1.8492227792739868e-01 1.9994279742240906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 2.1343495696783066e-02</internalNodes>
          <leafValues>
            -8.6192794144153595e-02 4.8719888925552368e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 -2.2514071315526962e-03</internalNodes>
          <leafValues>
            3.5809755325317383e-01 -7.6123438775539398e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 -4.4778124429285526e-03</internalNodes>
          <leafValues>
            -4.5578238368034363e-01 7.3516018688678741e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 3.9280336350202560e-03</internalNodes>
          <leafValues>
            6.2599055469036102e-02 -5.2695369720458984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 -4.5666974037885666e-03</internalNodes>
          <leafValues>
            -6.1827522516250610e-01 4.1984613984823227e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 -6.1424830928444862e-03</internalNodes>
          <leafValues>
            3.0607789754867554e-01 -9.1138295829296112e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1019 3.4258943051099777e-03</internalNodes>
          <leafValues>
            5.5657953023910522e-02 -5.3350126743316650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 3.3122287131845951e-03</internalNodes>
          <leafValues>
            -1.5935245156288147e-01 1.7000633478164673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 7.4128687381744385e-02</internalNodes>
          <leafValues>
            3.3975400030612946e-02 -6.4646822214126587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 -6.0862921178340912e-02</internalNodes>
          <leafValues>
            3.1012952327728271e-01 -9.1380268335342407e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 -4.3243117630481720e-02</internalNodes>
          <leafValues>
            -4.5051410794258118e-01 6.6722445189952850e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 -5.4576778784394264e-03</internalNodes>
          <leafValues>
            -4.8368638753890991e-01 5.5113438516855240e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 -2.1073617972433567e-03</internalNodes>
          <leafValues>
            2.3326623439788818e-01 -1.2007984519004822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 -1.1282963678240776e-02</internalNodes>
          <leafValues>
            2.9159554839134216e-01 -1.0025029629468918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 2.9302681796252728e-03</internalNodes>
          <leafValues>
            -8.5840485990047455e-02 3.3159431815147400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 -2.8825225308537483e-03</internalNodes>
          <leafValues>
            -5.3361582756042480e-01 5.7994876056909561e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 6.2230005860328674e-03</internalNodes>
          <leafValues>
            4.4393569231033325e-02 -5.3072142601013184e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 971 1.1437942739576101e-03</internalNodes>
          <leafValues>
            -9.5763660967350006e-02 2.8212538361549377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1052 1.2469270732253790e-03</internalNodes>
          <leafValues>
            6.5446242690086365e-02 -4.1902217268943787e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 -1.1369751766324043e-02</internalNodes>
          <leafValues>
            -7.0747911930084229e-01 3.4916084259748459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 1.0013033449649811e-01</internalNodes>
          <leafValues>
            -6.7160040140151978e-02 4.2184004187583923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 -2.6742245536297560e-03</internalNodes>
          <leafValues>
            1.7217047512531281e-01 -1.6229687631130219e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -3.4254738129675388e-03</internalNodes>
          <leafValues>
            2.9603767395019531e-01 -8.9177258312702179e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 1.5813322970643640e-03</internalNodes>
          <leafValues>
            4.8733744770288467e-02 -5.6422549486160278e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 2.7555555789149366e-05</internalNodes>
          <leafValues>
            -1.7079097032546997e-01 1.4066468179225922e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 466 -8.2116597332060337e-04</internalNodes>
          <leafValues>
            1.8260034918785095e-01 -1.3242910802364349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 -1.0168720036745071e-02</internalNodes>
          <leafValues>
            -4.1390055418014526e-01 6.5349683165550232e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 2.5848036631941795e-02</internalNodes>
          <leafValues>
            4.6910341829061508e-02 -4.7531116008758545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 5.9797330759465694e-03</internalNodes>
          <leafValues>
            4.5450355857610703e-02 -4.5701387524604797e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 -2.4257015902549028e-03</internalNodes>
          <leafValues>
            1.8431460857391357e-01 -1.1879430711269379e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 -4.1334740817546844e-02</internalNodes>
          <leafValues>
            3.0460721254348755e-01 -9.4910860061645508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 7.5982198119163513e-02</internalNodes>
          <leafValues>
            -6.5890170633792877e-02 3.3325287699699402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 -2.7852014682139270e-05</internalNodes>
          <leafValues>
            1.4771287143230438e-01 -1.4524473249912262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -1.4885163400322199e-03</internalNodes>
          <leafValues>
            -4.6987643837928772e-01 4.7233786433935165e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 -3.3519542776048183e-03</internalNodes>
          <leafValues>
            2.4128976464271545e-01 -9.3788638710975647e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 1.3348343782126904e-03</internalNodes>
          <leafValues>
            -9.9509775638580322e-02 2.9368522763252258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 3.2456549815833569e-03</internalNodes>
          <leafValues>
            -9.8895303905010223e-02 2.3363485932350159e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 4.2385179549455643e-03</internalNodes>
          <leafValues>
            5.9986904263496399e-02 -4.5745995640754700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 8.4751443937420845e-03</internalNodes>
          <leafValues>
            3.0937874689698219e-02 -6.7139619588851929e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 995 3.0964510515332222e-03</internalNodes>
          <leafValues>
            3.0879957601428032e-02 -6.2686437368392944e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 2.3455230984836817e-03</internalNodes>
          <leafValues>
            -1.3303077220916748e-01 1.6908498108386993e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>72</maxWeakCount>
      <stageThreshold>-1.4052674770355225e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 534 -8.4834604058414698e-04</internalNodes>
          <leafValues>
            4.6746683120727539e-01 -1.2498743087053299e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 1.1534148361533880e-03</internalNodes>
          <leafValues>
            -2.1341361105442047e-01 3.0533915758132935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 1.3660041615366936e-02</internalNodes>
          <leafValues>
            -1.5390963852405548e-01 3.2113197445869446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -1.3363182079046965e-03</internalNodes>
          <leafValues>
            2.4346974492073059e-01 -1.8074017763137817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1002 5.5064354091882706e-04</internalNodes>
          <leafValues>
            -1.9600959122180939e-01 2.1903340518474579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 2.8026416897773743e-02</internalNodes>
          <leafValues>
            -9.9956467747688293e-02 5.1314896345138550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 -9.8200759384781122e-04</internalNodes>
          <leafValues>
            2.0671010017395020e-01 -1.9585600495338440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 -1.9661948084831238e-02</internalNodes>
          <leafValues>
            -5.1859843730926514e-01 7.9988524317741394e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 5.7550622150301933e-03</internalNodes>
          <leafValues>
            -1.0230549424886703e-01 2.9102912545204163e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 4.8226406797766685e-03</internalNodes>
          <leafValues>
            -1.2503834068775177e-01 2.2606587409973145e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1025 -3.5137422382831573e-03</internalNodes>
          <leafValues>
            -6.8291509151458740e-01 4.6296034008264542e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 2.7717142074834555e-05</internalNodes>
          <leafValues>
            -2.1390475332736969e-01 1.3291628658771515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -2.2634968161582947e-02</internalNodes>
          <leafValues>
            4.0156257152557373e-01 -9.0922117233276367e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 -2.6544253341853619e-04</internalNodes>
          <leafValues>
            2.1944612264633179e-01 -1.5686984360218048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 1.7469950020313263e-02</internalNodes>
          <leafValues>
            5.9605021029710770e-02 -5.4529672861099243e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 3.6130528897047043e-03</internalNodes>
          <leafValues>
            5.2721742540597916e-02 -4.4890201091766357e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 -3.8260491564869881e-03</internalNodes>
          <leafValues>
            -5.1076781749725342e-01 4.7858215868473053e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 -4.6305969590321183e-04</internalNodes>
          <leafValues>
            2.0340332388877869e-01 -1.3007256388664246e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 -7.3791583999991417e-03</internalNodes>
          <leafValues>
            -5.4855078458786011e-01 5.1355980336666107e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -4.1331160813570023e-02</internalNodes>
          <leafValues>
            -3.7914556264877319e-01 6.2432620674371719e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 -1.4983891742303967e-03</internalNodes>
          <leafValues>
            -5.2967226505279541e-01 4.2461462318897247e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -2.5054097641259432e-03</internalNodes>
          <leafValues>
            2.0288434624671936e-01 -1.2341590225696564e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 -7.1871257387101650e-04</internalNodes>
          <leafValues>
            2.4784520268440247e-01 -9.8167583346366882e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -6.8983237724751234e-04</internalNodes>
          <leafValues>
            2.7780577540397644e-01 -9.7512029111385345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 4.8434769269078970e-04</internalNodes>
          <leafValues>
            -1.1704409867525101e-01 2.4324342608451843e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -3.6378027871251106e-03</internalNodes>
          <leafValues>
            -5.7295501232147217e-01 4.9037151038646698e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 -2.6648804545402527e-02</internalNodes>
          <leafValues>
            -6.0253041982650757e-01 3.6413222551345825e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -4.3416651897132397e-03</internalNodes>
          <leafValues>
            4.7109794616699219e-01 -5.9058945626020432e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 -2.7588163502514362e-03</internalNodes>
          <leafValues>
            -4.9160134792327881e-01 5.4663125425577164e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 987 4.7046472318470478e-03</internalNodes>
          <leafValues>
            3.7025094032287598e-02 -5.6842529773712158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 4.9029560759663582e-03</internalNodes>
          <leafValues>
            4.8207473009824753e-02 -4.2965477705001831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -7.0135248824954033e-04</internalNodes>
          <leafValues>
            2.2556030750274658e-01 -9.9117368459701538e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 2.7165210340172052e-03</internalNodes>
          <leafValues>
            4.3833449482917786e-02 -5.5271440744400024e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 8.9941755868494511e-04</internalNodes>
          <leafValues>
            -8.9474648237228394e-02 2.6415902376174927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 -1.7575379461050034e-03</internalNodes>
          <leafValues>
            -5.7822185754776001e-01 4.4655490666627884e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 2.2079560905694962e-02</internalNodes>
          <leafValues>
            -9.1862626373767853e-02 2.6927500963211060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 -2.4989219382405281e-03</internalNodes>
          <leafValues>
            1.9282613694667816e-01 -1.4004705846309662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 4.4558709487318993e-03</internalNodes>
          <leafValues>
            5.2965965121984482e-02 -4.6530798077583313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 8.9809950441122055e-03</internalNodes>
          <leafValues>
            -6.9099865853786469e-02 3.5005539655685425e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 -4.6078087761998177e-03</internalNodes>
          <leafValues>
            1.5373907983303070e-01 -1.5948937833309174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -8.9063167572021484e-02</internalNodes>
          <leafValues>
            4.8500600457191467e-01 -5.1386959850788116e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 4.8636873252689838e-03</internalNodes>
          <leafValues>
            5.1732856780290604e-02 -4.9787709116935730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 992 -5.4465518333017826e-03</internalNodes>
          <leafValues>
            1.5584819018840790e-01 -1.4326727390289307e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 6.4384475350379944e-02</internalNodes>
          <leafValues>
            3.1540591269731522e-02 -7.1331930160522461e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -9.3528348952531815e-03</internalNodes>
          <leafValues>
            -5.8800560235977173e-01 3.2534934580326080e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 6.5686285961419344e-04</internalNodes>
          <leafValues>
            -1.6972899436950684e-01 1.4208021759986877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 -6.5707243047654629e-03</internalNodes>
          <leafValues>
            3.1901842355728149e-01 -7.0233277976512909e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 7.0676081813871861e-03</internalNodes>
          <leafValues>
            3.0735086649656296e-02 -7.6451587677001953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -1.1614331044256687e-02</internalNodes>
          <leafValues>
            2.0416912436485291e-01 -1.0650242120027542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 -3.0933439731597900e-02</internalNodes>
          <leafValues>
            -3.5186296701431274e-01 6.3158944249153137e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 8.9404191821813583e-03</internalNodes>
          <leafValues>
            4.1301336139440536e-02 -5.2171415090560913e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 -3.0004943255335093e-04</internalNodes>
          <leafValues>
            1.8332102894783020e-01 -1.1965552717447281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 -4.2704585939645767e-03</internalNodes>
          <leafValues>
            -4.1220253705978394e-01 5.2136015146970749e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 9.1349193826317787e-04</internalNodes>
          <leafValues>
            -8.2035504281520844e-02 2.7817621827125549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 2.8089310973882675e-02</internalNodes>
          <leafValues>
            6.0909613966941833e-02 -3.7705209851264954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 -1.1489203898236156e-03</internalNodes>
          <leafValues>
            2.9547268152236938e-01 -7.8550107777118683e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 -8.5876882076263428e-04</internalNodes>
          <leafValues>
            1.6158875823020935e-01 -1.3613829016685486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 3.3645064104348421e-03</internalNodes>
          <leafValues>
            3.6055568605661392e-02 -5.5788111686706543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1034 -1.2699423357844353e-02</internalNodes>
          <leafValues>
            -4.2199519276618958e-01 4.3876208364963531e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 -1.3306856155395508e-01</internalNodes>
          <leafValues>
            -7.5723612308502197e-01 2.4755204096436501e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 4.9831219017505646e-02</internalNodes>
          <leafValues>
            2.5250671431422234e-02 -6.3122928142547607e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 5.8193420991301537e-03</internalNodes>
          <leafValues>
            2.2189516574144363e-02 -7.2821933031082153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -6.3158385455608368e-03</internalNodes>
          <leafValues>
            1.9480472803115845e-01 -1.0275462269783020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 -2.6879269629716873e-02</internalNodes>
          <leafValues>
            -4.3909311294555664e-01 4.5222271233797073e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 -1.6478844918310642e-03</internalNodes>
          <leafValues>
            2.7425831556320190e-01 -7.7650256454944611e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 4.4362144544720650e-03</internalNodes>
          <leafValues>
            3.2876692712306976e-02 -6.0907542705535889e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 -1.5154483262449503e-03</internalNodes>
          <leafValues>
            2.2985421121120453e-01 -8.5810013115406036e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 7.0627350360155106e-03</internalNodes>
          <leafValues>
            3.4827440977096558e-02 -5.9273594617843628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 4.5482232235372066e-03</internalNodes>
          <leafValues>
            -5.2113339304924011e-02 4.0603092312812805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 -3.9095789194107056e-02</internalNodes>
          <leafValues>
            2.5562492012977600e-01 -8.1410482525825500e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 -1.9122204976156354e-03</internalNodes>
          <leafValues>
            -6.5523076057434082e-01 3.1964879482984543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 5.1604928448796272e-03</internalNodes>
          <leafValues>
            2.8228869661688805e-02 -6.0336226224899292e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>63</maxWeakCount>
      <stageThreshold>-1.2550007104873657e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 532 -1.3708438724279404e-02</internalNodes>
          <leafValues>
            4.5314663648605347e-01 -1.2558805942535400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 1.2687301263213158e-02</internalNodes>
          <leafValues>
            -1.5584127604961395e-01 3.8753288984298706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 3.3966779708862305e-02</internalNodes>
          <leafValues>
            -1.1772038787603378e-01 4.0628942847251892e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 8.0258902162313461e-03</internalNodes>
          <leafValues>
            -1.4661933481693268e-01 4.0369525551795959e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -4.2836386710405350e-03</internalNodes>
          <leafValues>
            2.2167153656482697e-01 -1.9662868976593018e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 -2.7807329315692186e-03</internalNodes>
          <leafValues>
            -4.6929144859313965e-01 6.9577261805534363e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 1.9090694840997458e-03</internalNodes>
          <leafValues>
            5.9488739818334579e-02 -6.3101488351821899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 3.1442400068044662e-03</internalNodes>
          <leafValues>
            -1.1149841547012329e-01 3.0095639824867249e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -2.8418585658073425e-02</internalNodes>
          <leafValues>
            3.6157062649726868e-01 -9.6387691795825958e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -4.4032465666532516e-03</internalNodes>
          <leafValues>
            3.2977014780044556e-01 -9.8187342286109924e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 -2.6041134260594845e-03</internalNodes>
          <leafValues>
            2.8221642971038818e-01 -1.0142992436885834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 -5.8917067945003510e-03</internalNodes>
          <leafValues>
            -5.8254349231719971e-01 6.0040380805730820e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 998 1.3956660404801369e-03</internalNodes>
          <leafValues>
            -1.6574928164482117e-01 1.7746162414550781e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1022 -1.7630932852625847e-03</internalNodes>
          <leafValues>
            -5.7597070932388306e-01 6.2388133257627487e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 -1.3517161132767797e-03</internalNodes>
          <leafValues>
            -5.1934504508972168e-01 4.7232870012521744e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 -3.8743610493838787e-03</internalNodes>
          <leafValues>
            2.9165247082710266e-01 -9.9355563521385193e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 1.0973589494824409e-02</internalNodes>
          <leafValues>
            -7.7571205794811249e-02 3.4312543272972107e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 -3.5274624824523926e-03</internalNodes>
          <leafValues>
            -6.7513287067413330e-01 3.6897819489240646e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 -2.4239125195890665e-03</internalNodes>
          <leafValues>
            2.5701349973678589e-01 -1.0465545207262039e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 -8.3098262548446655e-03</internalNodes>
          <leafValues>
            2.6842510700225830e-01 -9.9635124206542969e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 -2.7831714600324631e-02</internalNodes>
          <leafValues>
            -3.9901316165924072e-01 6.5086022019386292e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 8.1690559163689613e-03</internalNodes>
          <leafValues>
            -1.1402101069688797e-01 2.2761905193328857e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 2.8635351918637753e-03</internalNodes>
          <leafValues>
            -1.4034478366374969e-01 1.8733198940753937e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 -2.1204156801104546e-03</internalNodes>
          <leafValues>
            -5.9949654340744019e-01 4.9501683562994003e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -9.4446074217557907e-04</internalNodes>
          <leafValues>
            -3.8145086169242859e-01 5.9254929423332214e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 2.1901372820138931e-03</internalNodes>
          <leafValues>
            3.6901079118251801e-02 -5.6260800361633301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 4.2550573125481606e-03</internalNodes>
          <leafValues>
            -9.8831087350845337e-02 2.3313422501087189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 4.2771790176630020e-03</internalNodes>
          <leafValues>
            4.2207289487123489e-02 -5.6859022378921509e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -7.8792609274387360e-03</internalNodes>
          <leafValues>
            2.2428077459335327e-01 -9.9518932402133942e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 561 -3.5514549817889929e-03</internalNodes>
          <leafValues>
            -5.6150603294372559e-01 3.9242122322320938e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 -6.8606354761868715e-04</internalNodes>
          <leafValues>
            2.1056549251079559e-01 -1.2413132935762405e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 5.2483025938272476e-03</internalNodes>
          <leafValues>
            3.4256864339113235e-02 -7.2566890716552734e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 -3.6910744383931160e-03</internalNodes>
          <leafValues>
            2.6440864801406860e-01 -8.9745096862316132e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 2.0369128324091434e-03</internalNodes>
          <leafValues>
            4.6990364789962769e-02 -5.3132331371307373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 3.8735207635909319e-03</internalNodes>
          <leafValues>
            -9.1540865600109100e-02 2.7486115694046021e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 6.0556940734386444e-03</internalNodes>
          <leafValues>
            5.3909529000520706e-02 -4.6437451243400574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 4.8301572678610682e-04</internalNodes>
          <leafValues>
            -1.6165176033973694e-01 1.3917934894561768e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 -1.4880476519465446e-02</internalNodes>
          <leafValues>
            -5.9634107351303101e-01 3.9811171591281891e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 2.9731846880167723e-03</internalNodes>
          <leafValues>
            3.0903076753020287e-02 -6.2935864925384521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 -1.1181155219674110e-02</internalNodes>
          <leafValues>
            3.5473996400833130e-01 -6.4499482512474060e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1009 -9.8370900377631187e-04</internalNodes>
          <leafValues>
            2.9858112335205078e-01 -8.4500424563884735e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 -1.0228222236037254e-03</internalNodes>
          <leafValues>
            2.7100124955177307e-01 -1.0033085197210312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 2.0134919323027134e-03</internalNodes>
          <leafValues>
            4.3533660471439362e-02 -5.4969471693038940e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 -3.1473359558731318e-03</internalNodes>
          <leafValues>
            3.1102818250656128e-01 -8.0141142010688782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 991 -2.9232497327029705e-03</internalNodes>
          <leafValues>
            -6.7808300256729126e-01 3.5025410354137421e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 -3.8992143236100674e-03</internalNodes>
          <leafValues>
            2.5711989402770996e-01 -8.4509201347827911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 -3.8403570652008057e-02</internalNodes>
          <leafValues>
            2.8463324904441833e-01 -7.5673028826713562e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 700 -2.2210094612091780e-03</internalNodes>
          <leafValues>
            -5.6876182556152344e-01 4.0759250521659851e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 989 6.9615743122994900e-03</internalNodes>
          <leafValues>
            -7.8118488192558289e-02 2.8128826618194580e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 -1.8219950143247843e-03</internalNodes>
          <leafValues>
            1.8647159636020660e-01 -1.3465921580791473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 1.0106971021741629e-03</internalNodes>
          <leafValues>
            5.7168632745742798e-02 -4.1419604420661926e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 -3.3746981061995029e-03</internalNodes>
          <leafValues>
            -5.2892911434173584e-01 4.0065344423055649e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1030 -8.5245687514543533e-03</internalNodes>
          <leafValues>
            -5.0935691595077515e-01 3.8823168724775314e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1012 -2.2426969371736050e-03</internalNodes>
          <leafValues>
            2.5891116261482239e-01 -8.8167145848274231e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 -5.9730862267315388e-03</internalNodes>
          <leafValues>
            -4.3465223908424377e-01 4.9864508211612701e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 -5.5482299067080021e-03</internalNodes>
          <leafValues>
            2.5288850069046021e-01 -9.3322932720184326e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 3.7344563007354736e-01</internalNodes>
          <leafValues>
            -4.9019347876310349e-02 4.3872711062431335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 -4.0881419554352760e-03</internalNodes>
          <leafValues>
            3.1952694058418274e-01 -7.7735908329486847e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 3.1661842949688435e-03</internalNodes>
          <leafValues>
            -1.0995075106620789e-01 1.7701222002506256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 -2.1666671335697174e-01</internalNodes>
          <leafValues>
            -4.5134860277175903e-01 4.9127347767353058e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 -3.1139418482780457e-02</internalNodes>
          <leafValues>
            2.5138390064239502e-01 -9.4933450222015381e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 9.1597874416038394e-04</internalNodes>
          <leafValues>
            -7.4231699109077454e-02 3.1368830800056458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 -6.1164153739809990e-03</internalNodes>
          <leafValues>
            -7.0417582988739014e-01 3.4018490463495255e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>77</maxWeakCount>
      <stageThreshold>-1.3230814933776855e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 522 -3.3400340471416712e-03</internalNodes>
          <leafValues>
            4.2352598905563354e-01 -1.2572944164276123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 -2.3890279699116945e-03</internalNodes>
          <leafValues>
            3.8169610500335693e-01 -1.4501731097698212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -2.4045775644481182e-03</internalNodes>
          <leafValues>
            3.4690696001052856e-01 -1.2821178138256073e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 1.2546034995466471e-03</internalNodes>
          <leafValues>
            -1.4823316037654877e-01 2.9894015192985535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 -1.8236635252833366e-02</internalNodes>
          <leafValues>
            3.0641126632690430e-01 -1.2427721172571182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 4.1921215597540140e-04</internalNodes>
          <leafValues>
            -1.8449674546718597e-01 1.7403297126293182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 -3.0837533995509148e-03</internalNodes>
          <leafValues>
            -6.2562137842178345e-01 3.4162398427724838e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 -3.4897932782769203e-03</internalNodes>
          <leafValues>
            2.0127655565738678e-01 -1.4677318930625916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 882 -3.4818234853446484e-03</internalNodes>
          <leafValues>
            2.9465374350547791e-01 -1.0961814969778061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 6.2356598675251007e-02</internalNodes>
          <leafValues>
            -9.8056003451347351e-02 3.1733244657516479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 -1.8334560096263885e-02</internalNodes>
          <leafValues>
            3.1992998719215393e-01 -7.8213296830654144e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 3.7803263403475285e-03</internalNodes>
          <leafValues>
            5.3678415715694427e-02 -5.0315982103347778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1027 -3.6906298249959946e-02</internalNodes>
          <leafValues>
            -6.3056147098541260e-01 3.8218058645725250e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 4.6968068927526474e-03</internalNodes>
          <leafValues>
            -1.1338837444782257e-01 2.6388064026832581e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 -1.1566210538148880e-02</internalNodes>
          <leafValues>
            1.6388712823390961e-01 -1.6043519973754883e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 3.1895786523818970e-03</internalNodes>
          <leafValues>
            6.0215596109628677e-02 -4.7157511115074158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -2.5480750948190689e-02</internalNodes>
          <leafValues>
            -5.5096846818923950e-01 3.9257630705833435e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 3.9267786778509617e-03</internalNodes>
          <leafValues>
            6.1174295842647552e-02 -4.1686600446701050e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 4.2923549190163612e-03</internalNodes>
          <leafValues>
            -6.9901801645755768e-02 3.6233785748481750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 1.5720827504992485e-03</internalNodes>
          <leafValues>
            -9.2891335487365723e-02 2.6970732212066650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 4.2968937195837498e-03</internalNodes>
          <leafValues>
            4.5402236282825470e-02 -6.1771476268768311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 5.8442405425012112e-03</internalNodes>
          <leafValues>
            3.4459017217159271e-02 -6.2251347303390503e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 2.6888614520430565e-03</internalNodes>
          <leafValues>
            3.6230482161045074e-02 -5.7353609800338745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 4.4175283983349800e-03</internalNodes>
          <leafValues>
            -6.4959764480590820e-02 3.7311050295829773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 1.4900951646268368e-03</internalNodes>
          <leafValues>
            -1.0781793296337128e-01 2.0226408541202545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 2.4665119126439095e-03</internalNodes>
          <leafValues>
            5.7804334908723831e-02 -4.1689205169677734e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 9.3985523562878370e-04</internalNodes>
          <leafValues>
            -1.4865192770957947e-01 1.3861793279647827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 -5.3606871515512466e-03</internalNodes>
          <leafValues>
            1.8524695932865143e-01 -1.1567704379558563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -4.6638157218694687e-03</internalNodes>
          <leafValues>
            1.6163532435894012e-01 -1.3586524128913879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 3.7256032228469849e-03</internalNodes>
          <leafValues>
            5.2170656621456146e-02 -4.2538973689079285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 -8.9184641838073730e-03</internalNodes>
          <leafValues>
            -5.0052535533905029e-01 4.7540370374917984e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 5.6020710617303848e-03</internalNodes>
          <leafValues>
            3.4621786326169968e-02 -5.4071390628814697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 -3.7551699206233025e-03</internalNodes>
          <leafValues>
            -3.9268767833709717e-01 5.2867397665977478e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 4.0759481489658356e-03</internalNodes>
          <leafValues>
            3.7209436297416687e-02 -4.7708320617675781e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 4.1836635209619999e-03</internalNodes>
          <leafValues>
            -5.8815345168113708e-02 3.6573976278305054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 -9.3902507796883583e-04</internalNodes>
          <leafValues>
            1.9424098730087280e-01 -1.1125016957521439e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 985 -9.9178254604339600e-03</internalNodes>
          <leafValues>
            -5.9317117929458618e-01 3.3418238162994385e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 3.3355036284774542e-03</internalNodes>
          <leafValues>
            -8.7399490177631378e-02 2.4422888457775116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 -3.4440397284924984e-03</internalNodes>
          <leafValues>
            2.9363137483596802e-01 -7.5259201228618622e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 2.1378418896347284e-03</internalNodes>
          <leafValues>
            5.6551665067672729e-02 -3.9630606770515442e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1005 -4.5215697027742863e-03</internalNodes>
          <leafValues>
            1.6443158686161041e-01 -1.1997994035482407e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -1.2263706885278225e-03</internalNodes>
          <leafValues>
            -2.6839572191238403e-01 7.8797832131385803e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 -7.3856199160218239e-03</internalNodes>
          <leafValues>
            -7.5282222032546997e-01 2.3323338478803635e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1044 1.1934632435441017e-02</internalNodes>
          <leafValues>
            3.9068166166543961e-02 -4.3301787972450256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 -4.2066089808940887e-03</internalNodes>
          <leafValues>
            3.1933805346488953e-01 -6.1786398291587830e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 -1.5679887728765607e-03</internalNodes>
          <leafValues>
            2.1744215488433838e-01 -9.4651907682418823e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 2.5083343498408794e-03</internalNodes>
          <leafValues>
            5.7137917727231979e-02 -3.3361336588859558e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 3.6224797368049622e-03</internalNodes>
          <leafValues>
            3.1345754861831665e-02 -5.7247912883758545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 -7.7814143151044846e-03</internalNodes>
          <leafValues>
            2.9652404785156250e-01 -6.6501826047897339e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 -4.1631370550021529e-04</internalNodes>
          <leafValues>
            2.2159980237483978e-01 -1.0610108822584152e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 4.7841453924775124e-03</internalNodes>
          <leafValues>
            3.3327136188745499e-02 -5.7043993473052979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 1.2740758247673512e-03</internalNodes>
          <leafValues>
            -7.9592645168304443e-02 2.4728350341320038e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 -2.0162630826234818e-02</internalNodes>
          <leafValues>
            -7.0677626132965088e-01 2.7118822559714317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 -2.5762226432561874e-02</internalNodes>
          <leafValues>
            -5.9367066621780396e-01 2.7015525847673416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 -1.1241633910685778e-03</internalNodes>
          <leafValues>
            2.9121127724647522e-01 -6.5690472722053528e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 2.9669383540749550e-02</internalNodes>
          <leafValues>
            3.4585461020469666e-02 -5.4837781190872192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 -6.3295168802142143e-03</internalNodes>
          <leafValues>
            2.3453639447689056e-01 -8.5172846913337708e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1046 4.0143523365259171e-03</internalNodes>
          <leafValues>
            3.5306803882122040e-02 -5.4817456007003784e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 -2.4633856955915689e-03</internalNodes>
          <leafValues>
            1.6164709627628326e-01 -1.1111633479595184e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -2.6468174532055855e-02</internalNodes>
          <leafValues>
            2.5775042176246643e-01 -7.2721429169178009e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1047 -2.5992670562118292e-03</internalNodes>
          <leafValues>
            -3.1405648589134216e-01 5.9779226779937744e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 -2.2960878908634186e-02</internalNodes>
          <leafValues>
            2.8405818343162537e-01 -6.8080194294452667e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 -1.6940593719482422e-02</internalNodes>
          <leafValues>
            3.0056476593017578e-01 -6.7668616771697998e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 1.7171052750200033e-03</internalNodes>
          <leafValues>
            -6.5253980457782745e-02 2.9430890083312988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -5.2873874083161354e-03</internalNodes>
          <leafValues>
            -4.5413893461227417e-01 4.3044254183769226e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -1.8073642626404762e-02</internalNodes>
          <leafValues>
            -3.4945023059844971e-01 5.2509855479001999e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 -2.0803229417651892e-03</internalNodes>
          <leafValues>
            -4.0171647071838379e-01 4.5229051262140274e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 -1.1218651343369856e-04</internalNodes>
          <leafValues>
            1.2830497324466705e-01 -1.4649079740047455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -6.6526420414447784e-03</internalNodes>
          <leafValues>
            -3.4429419040679932e-01 5.4524090141057968e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 -4.1576132178306580e-02</internalNodes>
          <leafValues>
            -5.5132204294204712e-01 3.2239176332950592e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 -3.2582432031631470e-03</internalNodes>
          <leafValues>
            2.1904261410236359e-01 -9.0739406645298004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 -4.4706808403134346e-03</internalNodes>
          <leafValues>
            2.2556288540363312e-01 -9.5258384943008423e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 -6.5750535577535629e-03</internalNodes>
          <leafValues>
            -4.8511472344398499e-01 4.1734144091606140e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 -3.7532784044742584e-02</internalNodes>
          <leafValues>
            2.0968079566955566e-01 -8.8354945182800293e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -1.2600638438016176e-03</internalNodes>
          <leafValues>
            2.2111406922340393e-01 -9.0988010168075562e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -2.3967802524566650e-02</internalNodes>
          <leafValues>
            -6.2524855136871338e-01 3.0603738501667976e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -3.1747903674840927e-02</internalNodes>
          <leafValues>
            -6.2007570266723633e-01 2.5801742449402809e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>84</maxWeakCount>
      <stageThreshold>-1.3265128135681152e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 801 -2.4247136898338795e-03</internalNodes>
          <leafValues>
            4.3507692217826843e-01 -1.1363404244184494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 3.6287805996835232e-03</internalNodes>
          <leafValues>
            -1.5781879425048828e-01 3.3899685740470886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 -4.2556263506412506e-03</internalNodes>
          <leafValues>
            2.2901295125484467e-01 -2.0403152704238892e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 1.6322638839483261e-03</internalNodes>
          <leafValues>
            -1.9230945408344269e-01 2.0004445314407349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 1.4746835455298424e-02</internalNodes>
          <leafValues>
            -1.2184409052133560e-01 3.9130899310112000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 -1.5139304101467133e-02</internalNodes>
          <leafValues>
            2.6918080449104309e-01 -1.4086124300956726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 -7.4753491207957268e-03</internalNodes>
          <leafValues>
            2.1792158484458923e-01 -1.6056208312511444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 2.3232740350067616e-03</internalNodes>
          <leafValues>
            -1.6489887237548828e-01 1.7108000814914703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 -2.7532558888196945e-03</internalNodes>
          <leafValues>
            -5.3275841474533081e-01 5.2368167787790298e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 -3.9793960750102997e-03</internalNodes>
          <leafValues>
            3.4057796001434326e-01 -8.0085732042789459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 7.1728855371475220e-02</internalNodes>
          <leafValues>
            -7.2147607803344727e-02 4.0667375922203064e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 -5.3792679682374001e-04</internalNodes>
          <leafValues>
            1.7865169048309326e-01 -1.4902706444263458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 6.0019297525286674e-03</internalNodes>
          <leafValues>
            7.1029536426067352e-02 -3.9921376109123230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 6.9427289068698883e-02</internalNodes>
          <leafValues>
            -9.5279395580291748e-02 2.6865223050117493e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -8.8401548564434052e-03</internalNodes>
          <leafValues>
            -5.3491175174713135e-01 5.0447739660739899e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 699 -1.4551014639437199e-02</internalNodes>
          <leafValues>
            1.9883459806442261e-01 -1.1586152762174606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 -1.7498439410701394e-03</internalNodes>
          <leafValues>
            2.2214990854263306e-01 -9.8238572478294373e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 -2.1636944264173508e-02</internalNodes>
          <leafValues>
            2.8814041614532471e-01 -8.2750618457794189e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 1.2786949053406715e-02</internalNodes>
          <leafValues>
            -8.7337315082550049e-02 2.6530647277832031e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -8.7271071970462799e-03</internalNodes>
          <leafValues>
            -5.3538525104522705e-01 5.0595279783010483e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1039 3.3185956999659538e-03</internalNodes>
          <leafValues>
            4.5733701437711716e-02 -4.4758048653602600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -1.2216938193887472e-03</internalNodes>
          <leafValues>
            1.5257745981216431e-01 -1.4963941276073456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 562 3.9857804775238037e-02</internalNodes>
          <leafValues>
            -8.5655666887760162e-02 2.6823255419731140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 2.4454984813928604e-03</internalNodes>
          <leafValues>
            4.6102020889520645e-02 -5.0574064254760742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -4.2114150524139404e-01</internalNodes>
          <leafValues>
            6.9476419687271118e-01 -3.2907195389270782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 2.3470625281333923e-02</internalNodes>
          <leafValues>
            -8.6790844798088074e-02 2.2723633050918579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 -1.1454307474195957e-02</internalNodes>
          <leafValues>
            2.5413584709167480e-01 -8.8991768658161163e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 5.0260839052498341e-03</internalNodes>
          <leafValues>
            3.8961157202720642e-02 -5.9463697671890259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 1.6196466749534011e-03</internalNodes>
          <leafValues>
            -9.0231269598007202e-02 2.6204809546470642e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 8.1676244735717773e-02</internalNodes>
          <leafValues>
            -8.0785289406776428e-02 2.5112318992614746e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 -5.4313270375132561e-03</internalNodes>
          <leafValues>
            1.6463221609592438e-01 -1.3186016678810120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 5.7006161659955978e-03</internalNodes>
          <leafValues>
            -1.3998855650424957e-01 1.4326113462448120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 -7.5926873832941055e-03</internalNodes>
          <leafValues>
            -5.5559343099594116e-01 3.7072587758302689e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 7.5261802412569523e-03</internalNodes>
          <leafValues>
            2.8434989973902702e-02 -5.8689045906066895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 -6.3516031950712204e-03</internalNodes>
          <leafValues>
            1.4447389543056488e-01 -1.4542055130004883e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 980 -7.6800247188657522e-04</internalNodes>
          <leafValues>
            1.8556322157382965e-01 -1.0404425859451294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 -4.4167470186948776e-03</internalNodes>
          <leafValues>
            -7.0306748151779175e-01 3.0874395743012428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1010 3.3405693247914314e-03</internalNodes>
          <leafValues>
            -6.6534630954265594e-02 3.4018290042877197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 1.1457607150077820e-02</internalNodes>
          <leafValues>
            3.3658623695373535e-02 -6.1056423187255859e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1000 -1.8547235522419214e-03</internalNodes>
          <leafValues>
            -7.4722522497177124e-01 2.2372998297214508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 -1.9720013439655304e-01</internalNodes>
          <leafValues>
            -5.9932583570480347e-01 2.9283462092280388e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 -2.6251156814396381e-03</internalNodes>
          <leafValues>
            -3.0683135986328125e-01 5.5391944944858551e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 -2.7104711532592773e-01</internalNodes>
          <leafValues>
            -6.4121168851852417e-01 2.6428909972310066e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 1.0233232751488686e-02</internalNodes>
          <leafValues>
            4.5153360813856125e-02 -3.6883556842803955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 4.0971953421831131e-03</internalNodes>
          <leafValues>
            4.1385501623153687e-02 -4.3035930395126343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 -8.8650803081691265e-04</internalNodes>
          <leafValues>
            1.6314724087715149e-01 -1.1271495372056961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 -4.1144760325551033e-03</internalNodes>
          <leafValues>
            -5.5176359415054321e-01 3.3540870994329453e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 -9.8663510289043188e-04</internalNodes>
          <leafValues>
            2.1676342189311981e-01 -8.5408315062522888e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 6.0831783339381218e-03</internalNodes>
          <leafValues>
            -8.7310679256916046e-02 2.3208071291446686e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -1.4624604955315590e-02</internalNodes>
          <leafValues>
            -5.9713214635848999e-01 3.0128041282296181e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 1.3654056005179882e-02</internalNodes>
          <leafValues>
            2.4816744029521942e-02 -6.2301605939865112e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 4.2229411192238331e-03</internalNodes>
          <leafValues>
            -7.3886208236217499e-02 2.4938605725765228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 1.3268929906189442e-03</internalNodes>
          <leafValues>
            4.0760166943073273e-02 -4.3510803580284119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -9.6903974190354347e-04</internalNodes>
          <leafValues>
            2.2486831247806549e-01 -7.8642837703227997e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 1.0329007636755705e-03</internalNodes>
          <leafValues>
            -7.3648050427436829e-02 2.6808246970176697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 -4.2711962014436722e-03</internalNodes>
          <leafValues>
            -4.0931078791618347e-01 4.7851666808128357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 983 -3.7627927958965302e-03</internalNodes>
          <leafValues>
            -5.0520634651184082e-01 3.0405685305595398e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 -1.7928264569491148e-03</internalNodes>
          <leafValues>
            3.3886525034904480e-01 -5.3929597139358521e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 3.9475625380873680e-03</internalNodes>
          <leafValues>
            3.4511350095272064e-02 -5.2250456809997559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 827 -4.4537894427776337e-03</internalNodes>
          <leafValues>
            2.2575919330120087e-01 -7.4650920927524567e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 -2.9974281787872314e-02</internalNodes>
          <leafValues>
            -6.0629475116729736e-01 3.4456655383110046e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 2.6775486767292023e-02</internalNodes>
          <leafValues>
            -8.8883727788925171e-02 2.0147153735160828e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 -4.4971965253353119e-03</internalNodes>
          <leafValues>
            -5.3158396482467651e-01 3.3491309732198715e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -1.5196309424936771e-02</internalNodes>
          <leafValues>
            2.8140705823898315e-01 -6.4074374735355377e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 -2.1833679638803005e-03</internalNodes>
          <leafValues>
            2.1953551471233368e-01 -8.5029341280460358e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 -5.4325433447957039e-03</internalNodes>
          <leafValues>
            -4.8182886838912964e-01 3.8184959441423416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 -3.9055421948432922e-03</internalNodes>
          <leafValues>
            -3.5678783059120178e-01 4.5511916279792786e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1017 -5.0043486990034580e-03</internalNodes>
          <leafValues>
            -3.5324424505233765e-01 4.9539435654878616e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 4.2052613571286201e-03</internalNodes>
          <leafValues>
            -7.6765090227127075e-02 2.4410718679428101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 -2.9198043048381805e-03</internalNodes>
          <leafValues>
            2.8657916188240051e-01 -9.1479435563087463e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 1.4442477375268936e-02</internalNodes>
          <leafValues>
            2.2604020312428474e-02 -7.7516084909439087e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 1.0879908688366413e-02</internalNodes>
          <leafValues>
            -8.9434660971164703e-02 1.8898591399192810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 1.2304648756980896e-01</internalNodes>
          <leafValues>
            2.9145279899239540e-02 -5.6789475679397583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 5.4486069828271866e-02</internalNodes>
          <leafValues>
            -8.0465197563171387e-02 2.1073351800441742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 -1.0112209245562553e-02</internalNodes>
          <leafValues>
            2.5688818097114563e-01 -7.3113977909088135e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 -4.3551158159971237e-03</internalNodes>
          <leafValues>
            -4.0537205338478088e-01 5.1149621605873108e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 2.8712721541523933e-03</internalNodes>
          <leafValues>
            -8.9186541736125946e-02 2.0391693711280823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 2.4744076654314995e-02</internalNodes>
          <leafValues>
            3.1359996646642685e-02 -5.9586691856384277e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 6.0209888033568859e-03</internalNodes>
          <leafValues>
            -8.2612000405788422e-02 2.1787849068641663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 6.0595902614295483e-03</internalNodes>
          <leafValues>
            4.7610606998205185e-02 -3.5010379552841187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -2.1957855671644211e-02</internalNodes>
          <leafValues>
            2.2477181255817413e-01 -7.5377546250820160e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 -3.9967135526239872e-03</internalNodes>
          <leafValues>
            4.3043723702430725e-01 -3.9885677397251129e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 -2.0381226204335690e-03</internalNodes>
          <leafValues>
            -5.8131587505340576e-01 3.2071832567453384e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 337 3.8902673404663801e-03</internalNodes>
          <leafValues>
            -6.0279250144958496e-02 2.9424437880516052e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>82</maxWeakCount>
      <stageThreshold>-1.2607949972152710e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 798 -1.9003680208697915e-03</internalNodes>
          <leafValues>
            4.8600798845291138e-01 -7.5834542512893677e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 1.5605278313159943e-03</internalNodes>
          <leafValues>
            -1.9763922691345215e-01 2.5329649448394775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 -4.8138713464140892e-03</internalNodes>
          <leafValues>
            3.5302931070327759e-01 -1.2585695087909698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 5.7447804138064384e-03</internalNodes>
          <leafValues>
            -1.5453046560287476e-01 3.5572248697280884e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 3.2787662930786610e-03</internalNodes>
          <leafValues>
            -1.8419209122657776e-01 1.6216333210468292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 2.8142044320702553e-03</internalNodes>
          <leafValues>
            -9.4009101390838623e-02 2.7667456865310669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 1.8096582498401403e-03</internalNodes>
          <leafValues>
            -8.9050479233264923e-02 2.9622453451156616e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 7.2106244042515755e-03</internalNodes>
          <leafValues>
            -1.0854976624250412e-01 2.2157947719097137e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 1.3368867337703705e-02</internalNodes>
          <leafValues>
            5.8126326650381088e-02 -3.8564166426658630e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 1.6755410470068455e-03</internalNodes>
          <leafValues>
            -6.9541916251182556e-02 3.6275833845138550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 -4.5782830566167831e-03</internalNodes>
          <leafValues>
            -5.6317430734634399e-01 3.9351724088191986e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 3.6364984698593616e-03</internalNodes>
          <leafValues>
            -1.5140864253044128e-01 1.4790520071983337e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 -1.1279541999101639e-02</internalNodes>
          <leafValues>
            -4.8907181620597839e-01 5.1109701395034790e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 -1.2224027886986732e-02</internalNodes>
          <leafValues>
            -6.0496371984481812e-01 3.5609807819128036e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 -2.8662174940109253e-02</internalNodes>
          <leafValues>
            2.4556699395179749e-01 -9.9369116127490997e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 6.7924216389656067e-02</internalNodes>
          <leafValues>
            -7.8038521111011505e-02 3.3691942691802979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 2.2719642147421837e-03</internalNodes>
          <leafValues>
            5.8022607117891312e-02 -4.7124773263931274e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 8.5627539083361626e-03</internalNodes>
          <leafValues>
            3.4671626985073090e-02 -4.6883812546730042e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 1.1866856366395950e-03</internalNodes>
          <leafValues>
            -8.0339640378952026e-02 2.5030750036239624e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 8.1023329403251410e-04</internalNodes>
          <leafValues>
            -8.0605715513229370e-02 2.5741192698478699e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 -4.0647285059094429e-03</internalNodes>
          <leafValues>
            -5.0938653945922852e-01 4.0403041988611221e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 -1.9617568701505661e-02</internalNodes>
          <leafValues>
            -5.4703706502914429e-01 3.5078343003988266e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 6.9989012554287910e-03</internalNodes>
          <leafValues>
            2.6246270164847374e-02 -6.0453557968139648e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -6.2460554763674736e-03</internalNodes>
          <leafValues>
            2.3062629997730255e-01 -8.3763726055622101e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 7.5731135439127684e-04</internalNodes>
          <leafValues>
            -9.5188923180103302e-02 2.3367822170257568e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -3.2256892882287502e-03</internalNodes>
          <leafValues>
            2.1003848314285278e-01 -1.2173316627740860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 -2.8797222767025232e-03</internalNodes>
          <leafValues>
            -4.8621371388435364e-01 4.3998546898365021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 5.9399371966719627e-03</internalNodes>
          <leafValues>
            2.7645273134112358e-02 -6.2591820955276489e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 742 -5.4768389090895653e-03</internalNodes>
          <leafValues>
            2.5695452094078064e-01 -8.1276804208755493e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 -2.2785080596804619e-02</internalNodes>
          <leafValues>
            -6.7479509115219116e-01 2.9845010489225388e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 -6.0453559271991253e-03</internalNodes>
          <leafValues>
            -4.5132589340209961e-01 4.0413774549961090e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 5.9022027999162674e-03</internalNodes>
          <leafValues>
            4.6321801841259003e-02 -3.9377251267433167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 -1.1740738991647959e-03</internalNodes>
          <leafValues>
            2.2063454985618591e-01 -8.9038714766502380e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 -3.7963264621794224e-03</internalNodes>
          <leafValues>
            1.7901860177516937e-01 -1.0518371313810349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 2.4132090620696545e-03</internalNodes>
          <leafValues>
            -9.3182116746902466e-02 2.9489630460739136e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 4.5318575575947762e-04</internalNodes>
          <leafValues>
            -1.4386458694934845e-01 1.3717848062515259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1029 1.8930386751890182e-02</internalNodes>
          <leafValues>
            3.3168405294418335e-02 -5.5337232351303101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 -2.6878318749368191e-03</internalNodes>
          <leafValues>
            -5.4439735412597656e-01 3.1048862263560295e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 -3.9407592266798019e-03</internalNodes>
          <leafValues>
            -6.5507227182388306e-01 2.4424355477094650e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 2.1629813127219677e-03</internalNodes>
          <leafValues>
            -1.0160741209983826e-01 1.8277852237224579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 -2.9370808042585850e-03</internalNodes>
          <leafValues>
            -4.7847637534141541e-01 3.8538910448551178e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 3.8221649825572968e-02</internalNodes>
          <leafValues>
            -7.6206430792808533e-02 2.3375664651393890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -3.1483019702136517e-03</internalNodes>
          <leafValues>
            2.5192636251449585e-01 -7.3695883154869080e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 -4.5907422900199890e-03</internalNodes>
          <leafValues>
            -6.2766075134277344e-01 2.8896089643239975e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 -9.5378428697586060e-02</internalNodes>
          <leafValues>
            -7.4559724330902100e-01 2.1207747980952263e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 2.0872952882200480e-03</internalNodes>
          <leafValues>
            -8.7810918688774109e-02 2.0629811286926270e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 -6.9244997575879097e-03</internalNodes>
          <leafValues>
            1.8590562045574188e-01 -9.8790608346462250e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 2.4594084825366735e-03</internalNodes>
          <leafValues>
            -1.0049589723348618e-01 2.2963477671146393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1021 -5.2931695245206356e-03</internalNodes>
          <leafValues>
            -4.5924744009971619e-01 4.3104480952024460e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 994 4.8847724683582783e-03</internalNodes>
          <leafValues>
            4.6008609235286713e-02 -4.4277390837669373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 1.4400177169591188e-03</internalNodes>
          <leafValues>
            -5.9334080666303635e-02 3.0132320523262024e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 -8.6052305996417999e-03</internalNodes>
          <leafValues>
            1.9737368822097778e-01 -8.9747570455074310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 -6.1248587444424629e-03</internalNodes>
          <leafValues>
            -4.5141929388046265e-01 3.8760874420404434e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 -1.8148655071854591e-03</internalNodes>
          <leafValues>
            2.2768247127532959e-01 -8.2637414336204529e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 -8.5119507275521755e-04</internalNodes>
          <leafValues>
            1.9616322219371796e-01 -1.0013028979301453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 1.4472046867012978e-02</internalNodes>
          <leafValues>
            -8.8336527347564697e-02 1.9660694897174835e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 1.4135142788290977e-02</internalNodes>
          <leafValues>
            -6.4112767577171326e-02 3.1887489557266235e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 4.8004039563238621e-03</internalNodes>
          <leafValues>
            4.8681098967790604e-02 -4.6234726905822754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 -3.3503584563732147e-02</internalNodes>
          <leafValues>
            2.5094386935234070e-01 -8.0808885395526886e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 2.4153569247573614e-03</internalNodes>
          <leafValues>
            -7.2777584195137024e-02 2.6076248288154602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -1.3153228908777237e-02</internalNodes>
          <leafValues>
            2.3979008197784424e-01 -7.6283767819404602e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 -8.5048296023160219e-04</internalNodes>
          <leafValues>
            -3.2108953595161438e-01 5.7150222361087799e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 2.0031477324664593e-03</internalNodes>
          <leafValues>
            -7.5618073344230652e-02 2.3024985194206238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 -3.9609652012586594e-03</internalNodes>
          <leafValues>
            -4.3856775760650635e-01 3.7756573408842087e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 5.9846425428986549e-03</internalNodes>
          <leafValues>
            3.5378426313400269e-02 -4.7760033607482910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 2.0205255597829819e-02</internalNodes>
          <leafValues>
            -8.0130979418754578e-02 2.2919151186943054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 -2.7492402587085962e-03</internalNodes>
          <leafValues>
            2.1395626664161682e-01 -7.6452419161796570e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 -8.3101191557943821e-04</internalNodes>
          <leafValues>
            1.6961804032325745e-01 -9.9106967449188232e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 -1.8657972104847431e-03</internalNodes>
          <leafValues>
            -3.8131290674209595e-01 4.6056091785430908e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 2.0824437960982323e-03</internalNodes>
          <leafValues>
            6.4966239035129547e-02 -2.3824627697467804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 -4.4267112389206886e-03</internalNodes>
          <leafValues>
            -3.5809823870658875e-01 4.6749643981456757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 1.3552411692216992e-03</internalNodes>
          <leafValues>
            -1.2307690829038620e-01 1.3934792578220367e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 -4.4114869087934494e-03</internalNodes>
          <leafValues>
            2.6617470383644104e-01 -7.4502207338809967e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 5.2309304010123014e-04</internalNodes>
          <leafValues>
            -1.0876630991697311e-01 1.5687976777553558e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 976 6.4505764748901129e-04</internalNodes>
          <leafValues>
            -8.0842182040214539e-02 2.0263716578483582e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 2.0405012182891369e-03</internalNodes>
          <leafValues>
            -6.2390543520450592e-02 3.3067914843559265e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 1.9838459789752960e-02</internalNodes>
          <leafValues>
            2.3488542065024376e-02 -8.1695795059204102e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 2.3998366668820381e-03</internalNodes>
          <leafValues>
            4.1017178446054459e-02 -3.7197592854499817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 -1.1092903092503548e-02</internalNodes>
          <leafValues>
            -5.5750596523284912e-01 2.9520254582166672e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 981 1.4876715838909149e-02</internalNodes>
          <leafValues>
            -6.5797492861747742e-02 2.5957426428794861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 -3.0385032296180725e-02</internalNodes>
          <leafValues>
            2.2640630602836609e-01 -7.6991938054561615e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 1.2216348201036453e-02</internalNodes>
          <leafValues>
            -7.0106968283653259e-02 2.4013392627239227e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 15 -->
    <_>
      <maxWeakCount>94</maxWeakCount>
      <stageThreshold>-1.2798616886138916e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 801 -3.8322431501001120e-03</internalNodes>
          <leafValues>
            4.8065602779388428e-01 -4.9388073384761810e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 2.5449637323617935e-03</internalNodes>
          <leafValues>
            -1.7564620077610016e-01 2.5865191221237183e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -5.4743299260735512e-03</internalNodes>
          <leafValues>
            4.9321442842483521e-01 -7.0596724748611450e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 1.5188493765890598e-02</internalNodes>
          <leafValues>
            -1.8555639684200287e-01 1.5278494358062744e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 7.5815798481926322e-04</internalNodes>
          <leafValues>
            -1.5043407678604126e-01 1.8612807989120483e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 -3.4232349134981632e-03</internalNodes>
          <leafValues>
            -4.5882478356361389e-01 4.3279532343149185e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 2.4103666655719280e-03</internalNodes>
          <leafValues>
            -8.4217190742492676e-02 2.6687353849411011e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 -2.3144368082284927e-02</internalNodes>
          <leafValues>
            2.9155749082565308e-01 -9.9449791014194489e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 -4.2331898584961891e-03</internalNodes>
          <leafValues>
            -3.7696760892868042e-01 8.0511704087257385e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 4.9294121563434601e-03</internalNodes>
          <leafValues>
            -1.3016121089458466e-01 1.8470372259616852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 -2.7466980100143701e-05</internalNodes>
          <leafValues>
            1.4074377715587616e-01 -1.7928679287433624e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 2.2430901881307364e-03</internalNodes>
          <leafValues>
            -1.4674974977970123e-01 1.5197925269603729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 7.5493026524782181e-03</internalNodes>
          <leafValues>
            2.4894557893276215e-02 -6.5740859508514404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 -3.3066330943256617e-03</internalNodes>
          <leafValues>
            1.8501703441143036e-01 -1.1837758123874664e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 6.9540860131382942e-03</internalNodes>
          <leafValues>
            -7.3770649731159210e-02 2.9017251729965210e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 -8.6210696026682854e-03</internalNodes>
          <leafValues>
            2.0990766584873199e-01 -1.0644201189279556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 978 -6.0504255816340446e-04</internalNodes>
          <leafValues>
            2.2373022139072418e-01 -9.6104651689529419e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 -4.5433510094881058e-03</internalNodes>
          <leafValues>
            -5.4173427820205688e-01 4.7511249780654907e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 -2.2248399909585714e-03</internalNodes>
          <leafValues>
            -4.6854707598686218e-01 3.8701556622982025e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -5.3389102220535278e-02</internalNodes>
          <leafValues>
            2.9293462634086609e-01 -7.2517670691013336e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 4.6098522841930389e-02</internalNodes>
          <leafValues>
            -1.0042577981948853e-01 2.3779328167438507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 7.7845109626650810e-03</internalNodes>
          <leafValues>
            3.7205196917057037e-02 -4.9194374680519104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 6.0175172984600067e-03</internalNodes>
          <leafValues>
            4.4034618884325027e-02 -4.3780878186225891e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 4.8966710455715656e-03</internalNodes>
          <leafValues>
            -1.0375351458787918e-01 1.9480220973491669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 -3.1284091528505087e-03</internalNodes>
          <leafValues>
            2.3669239878654480e-01 -9.6020378172397614e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 -1.3859109021723270e-03</internalNodes>
          <leafValues>
            2.8487151861190796e-01 -7.2190955281257629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 2.6260318700224161e-03</internalNodes>
          <leafValues>
            -8.5511997342109680e-02 3.0152606964111328e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 1.7782470583915710e-01</internalNodes>
          <leafValues>
            -6.4100205898284912e-02 3.3825826644897461e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 1.7538113519549370e-02</internalNodes>
          <leafValues>
            5.9994459152221680e-02 -3.5529783368110657e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 -3.2135979272425175e-03</internalNodes>
          <leafValues>
            1.3668337464332581e-01 -1.3979049026966095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 6.1371903866529465e-03</internalNodes>
          <leafValues>
            -6.2439329922199249e-02 3.0614212155342102e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -4.6563488431274891e-03</internalNodes>
          <leafValues>
            -4.3073609471321106e-01 4.9068968743085861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 -4.0680947713553905e-03</internalNodes>
          <leafValues>
            -4.6810126304626465e-01 3.7441805005073547e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 1.4199400320649147e-03</internalNodes>
          <leafValues>
            -8.7975829839706421e-02 2.1591611206531525e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 3.5254685208201408e-03</internalNodes>
          <leafValues>
            4.6650484204292297e-02 -4.3687531352043152e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 1.8623860552906990e-02</internalNodes>
          <leafValues>
            -7.6216101646423340e-02 2.3812168836593628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 -2.6926528662443161e-02</internalNodes>
          <leafValues>
            -6.7117422819137573e-01 2.9464269056916237e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 2.2593191824853420e-03</internalNodes>
          <leafValues>
            2.8521748259663582e-02 -5.4787307977676392e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 1.7519816174171865e-04</internalNodes>
          <leafValues>
            -1.6111046075820923e-01 1.0367503762245178e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 1.0614154860377312e-02</internalNodes>
          <leafValues>
            4.5461904257535934e-02 -3.8087964057922363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 -4.4702589511871338e-03</internalNodes>
          <leafValues>
            1.4304992556571960e-01 -1.3372300565242767e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 6.2367701902985573e-03</internalNodes>
          <leafValues>
            -7.7783808112144470e-02 2.1545551717281342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 4.6502514742314816e-03</internalNodes>
          <leafValues>
            4.6132039278745651e-02 -3.7130251526832581e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 -4.3315230868756771e-03</internalNodes>
          <leafValues>
            -4.1549521684646606e-01 3.8484618067741394e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 -1.6567837446928024e-03</internalNodes>
          <leafValues>
            -3.4637498855590820e-01 4.6623144298791885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 4.7653233632445335e-03</internalNodes>
          <leafValues>
            -5.0808548927307129e-02 3.4609997272491455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 -3.2579647377133369e-03</internalNodes>
          <leafValues>
            2.6948198676109314e-01 -8.5287831723690033e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 2.3307730443775654e-03</internalNodes>
          <leafValues>
            -7.4774339795112610e-02 2.3053503036499023e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 -2.7928136289119720e-02</internalNodes>
          <leafValues>
            1.9429244101047516e-01 -8.7820984423160553e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 -9.8205050453543663e-03</internalNodes>
          <leafValues>
            -5.9664642810821533e-01 3.1795132905244827e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 4.9811266362667084e-03</internalNodes>
          <leafValues>
            -1.1911241710186005e-01 1.5268225967884064e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -2.4869772605597973e-03</internalNodes>
          <leafValues>
            -3.8041505217552185e-01 4.4293139129877090e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 5.4475376382470131e-03</internalNodes>
          <leafValues>
            -4.6219147741794586e-02 3.9531415700912476e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 -2.1438062191009521e-02</internalNodes>
          <leafValues>
            -5.2191144227981567e-01 3.4259662032127380e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 -4.1901203803718090e-03</internalNodes>
          <leafValues>
            -5.2377271652221680e-01 2.8632357716560364e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 -4.7237933613359928e-03</internalNodes>
          <leafValues>
            1.8694585561752319e-01 -8.3333678543567657e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 1.2320578098297119e-03</internalNodes>
          <leafValues>
            -9.6744544804096222e-02 1.8287587165832520e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 2.0271677523851395e-02</internalNodes>
          <leafValues>
            -6.4628154039382935e-02 2.7641129493713379e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -1.0729704797267914e-01</internalNodes>
          <leafValues>
            4.3015307188034058e-01 -3.8674801588058472e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 -4.0820333361625671e-01</internalNodes>
          <leafValues>
            5.0520670413970947e-01 -3.0450601130723953e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 4.4355981051921844e-02</internalNodes>
          <leafValues>
            -9.2204704880714417e-02 1.7342080175876617e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 -1.0999260703101754e-03</internalNodes>
          <leafValues>
            2.0996508002281189e-01 -7.7222190797328949e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 -3.2928451895713806e-02</internalNodes>
          <leafValues>
            2.7598264813423157e-01 -6.4115919172763824e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 2.3981094360351562e-02</internalNodes>
          <leafValues>
            2.5229524821043015e-02 -6.9560426473617554e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 4.1703339666128159e-03</internalNodes>
          <leafValues>
            2.9712976887822151e-02 -4.8132696747779846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 -1.4920771354809403e-03</internalNodes>
          <leafValues>
            1.6165184974670410e-01 -9.6420668065547943e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 1.8172110430896282e-03</internalNodes>
          <leafValues>
            4.2247310280799866e-02 -3.5703054070472717e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 -2.5937356986105442e-03</internalNodes>
          <leafValues>
            2.2665317356586456e-01 -6.9081544876098633e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 -2.4995308369398117e-02</internalNodes>
          <leafValues>
            -6.3855916261672974e-01 2.8458235785365105e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 1.2001263909041882e-02</internalNodes>
          <leafValues>
            1.4999576844274998e-02 -7.8175085783004761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 2.2153530735522509e-03</internalNodes>
          <leafValues>
            -8.8839285075664520e-02 1.8819671869277954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 2.7237991162110120e-05</internalNodes>
          <leafValues>
            -1.4949426054954529e-01 9.8739065229892731e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 -2.6735704392194748e-02</internalNodes>
          <leafValues>
            -4.5522138476371765e-01 3.2516691833734512e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 -2.3417242337018251e-03</internalNodes>
          <leafValues>
            -3.1453001499176025e-01 4.7598775476217270e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 4.7831580042839050e-02</internalNodes>
          <leafValues>
            2.1954061463475227e-02 -6.1162966489791870e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 -5.7228151708841324e-03</internalNodes>
          <leafValues>
            -6.3381904363632202e-01 2.0299639552831650e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 3.4780064597725868e-03</internalNodes>
          <leafValues>
            3.1021401286125183e-02 -4.2342424392700195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 -5.4140854626893997e-03</internalNodes>
          <leafValues>
            4.7739461064338684e-01 -3.4031655639410019e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 1.5283382963389158e-03</internalNodes>
          <leafValues>
            -9.6935935318470001e-02 1.9429819285869598e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 -8.6789112538099289e-03</internalNodes>
          <leafValues>
            2.4826894700527191e-01 -6.0082063078880310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 3.0333681497722864e-03</internalNodes>
          <leafValues>
            -7.4087560176849365e-02 2.6165533065795898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 6.5222466364502907e-03</internalNodes>
          <leafValues>
            3.0176062136888504e-02 -5.5570882558822632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 5.9719551354646683e-03</internalNodes>
          <leafValues>
            2.3057831451296806e-02 -5.7078248262405396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 -1.3977952767163515e-03</internalNodes>
          <leafValues>
            1.5342144668102264e-01 -9.8401337862014771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 5.9919534251093864e-03</internalNodes>
          <leafValues>
            -3.9796624332666397e-02 3.5881185531616211e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 2.6286500506103039e-03</internalNodes>
          <leafValues>
            -9.3140766024589539e-02 1.6334943473339081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 -4.4777179136872292e-03</internalNodes>
          <leafValues>
            -4.8081240057945251e-01 3.2935630530118942e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 5.2724601700901985e-03</internalNodes>
          <leafValues>
            3.0787551775574684e-02 -4.5133110880851746e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1049 -3.2540475949645042e-03</internalNodes>
          <leafValues>
            -4.7695344686508179e-01 2.8554188087582588e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 1.8083681166172028e-01</internalNodes>
          <leafValues>
            2.7366345748305321e-02 -4.9431446194648743e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 2.7535988483577967e-03</internalNodes>
          <leafValues>
            1.9968675449490547e-02 -6.4471620321273804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -1.4123708009719849e-02</internalNodes>
          <leafValues>
            -5.2748751640319824e-01 2.4596616625785828e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 -3.2076485455036163e-02</internalNodes>
          <leafValues>
            -7.2171974182128906e-01 1.6940405592322350e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 -3.2569766044616699e-02</internalNodes>
          <leafValues>
            2.2400286793708801e-01 -6.3403561711311340e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 16 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.2990239858627319e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 728 1.1235726065933704e-02</internalNodes>
          <leafValues>
            -1.2534695863723755e-01 3.9147180318832397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 5.0947451964020729e-03</internalNodes>
          <leafValues>
            -1.2666413187980652e-01 4.0618515014648438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -1.5323986299335957e-03</internalNodes>
          <leafValues>
            2.8940162062644958e-01 -1.4350101351737976e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 3.7766513414680958e-03</internalNodes>
          <leafValues>
            -1.9189934432506561e-01 1.4756591618061066e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 4.8757870681583881e-03</internalNodes>
          <leafValues>
            -1.2341982126235962e-01 2.3298588395118713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 3.1278211623430252e-02</internalNodes>
          <leafValues>
            -7.6286941766738892e-02 3.4027433395385742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 6.3753505237400532e-03</internalNodes>
          <leafValues>
            7.3992513120174408e-02 -3.2609656453132629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 -9.8742637783288956e-04</internalNodes>
          <leafValues>
            2.4873960018157959e-01 -9.0153135359287262e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 -3.0144110321998596e-02</internalNodes>
          <leafValues>
            -5.1088541746139526e-01 5.0071869045495987e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 4.7727730125188828e-03</internalNodes>
          <leafValues>
            5.1353454589843750e-02 -4.1142973303794861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 6.4554966986179352e-02</internalNodes>
          <leafValues>
            4.5133572071790695e-02 -4.8264691233634949e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 8.0438675358891487e-03</internalNodes>
          <leafValues>
            -6.3803412020206451e-02 3.0405151844024658e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1051 1.0576066561043262e-03</internalNodes>
          <leafValues>
            4.9984093755483627e-02 -3.3949175477027893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 6.8522170186042786e-03</internalNodes>
          <leafValues>
            3.5091523081064224e-02 -6.7847234010696411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 -1.7977621406316757e-02</internalNodes>
          <leafValues>
            -3.7503832578659058e-01 4.0370170027017593e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 -2.9955487698316574e-02</internalNodes>
          <leafValues>
            -4.2023807764053345e-01 4.2222321033477783e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 2.0934976637363434e-02</internalNodes>
          <leafValues>
            4.3809924274682999e-02 -4.1159108281135559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 -1.0348223149776459e-03</internalNodes>
          <leafValues>
            1.7594149708747864e-01 -1.0171056538820267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 1.1026043444871902e-02</internalNodes>
          <leafValues>
            3.7518307566642761e-02 -4.9795153737068176e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 4.1434396989643574e-03</internalNodes>
          <leafValues>
            -7.7400334179401398e-02 2.3505100607872009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 -1.4838734641671181e-03</internalNodes>
          <leafValues>
            2.9909220337867737e-01 -9.2648021876811981e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1025 4.0641101077198982e-03</internalNodes>
          <leafValues>
            3.8187902420759201e-02 -5.9566622972488403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 -2.6055248454213142e-03</internalNodes>
          <leafValues>
            1.4647382497787476e-01 -1.1769902706146240e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -1.8873009830713272e-02</internalNodes>
          <leafValues>
            2.0791313052177429e-01 -9.1127894818782806e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 1.0428125038743019e-02</internalNodes>
          <leafValues>
            4.3083548545837402e-02 -4.1407048702239990e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 1.9560819491744041e-03</internalNodes>
          <leafValues>
            -6.5898597240447998e-02 2.6488196849822998e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 6.1143590137362480e-03</internalNodes>
          <leafValues>
            4.7718580812215805e-02 -4.3339842557907104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 3.9817169308662415e-03</internalNodes>
          <leafValues>
            2.8663935139775276e-02 -5.4472506046295166e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 -9.0858177281916142e-04</internalNodes>
          <leafValues>
            1.2656490504741669e-01 -1.3804104924201965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 -5.1833119243383408e-02</internalNodes>
          <leafValues>
            2.9838389158248901e-01 -6.4876683056354523e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 -6.1461031436920166e-02</internalNodes>
          <leafValues>
            2.2751982510089874e-01 -7.7075794339179993e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 -3.8890805444680154e-04</internalNodes>
          <leafValues>
            1.4823918044567108e-01 -1.2443733215332031e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 6.3632195815443993e-03</internalNodes>
          <leafValues>
            3.3928975462913513e-02 -5.5825293064117432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 2.3877150379121304e-03</internalNodes>
          <leafValues>
            -6.0555700212717056e-02 2.9875907301902771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 2.1584378555417061e-03</internalNodes>
          <leafValues>
            2.6707226410508156e-02 -6.5327596664428711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 972 1.3073299778625369e-03</internalNodes>
          <leafValues>
            -6.5057143568992615e-02 2.8509995341300964e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1023 2.7173646230949089e-05</internalNodes>
          <leafValues>
            -1.4736446738243103e-01 1.1435943096876144e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 2.5558518245816231e-03</internalNodes>
          <leafValues>
            2.2957315668463707e-02 -6.1825275421142578e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 4.4789682142436504e-03</internalNodes>
          <leafValues>
            3.6877695471048355e-02 -4.1827708482742310e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 -4.0298998355865479e-02</internalNodes>
          <leafValues>
            -6.8164646625518799e-01 2.1755648776888847e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 -3.2729938626289368e-02</internalNodes>
          <leafValues>
            -5.4164266586303711e-01 2.6013873517513275e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1011 -1.6982981469482183e-03</internalNodes>
          <leafValues>
            3.5175332427024841e-01 -4.7216285020112991e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 3.6859638057649136e-03</internalNodes>
          <leafValues>
            4.9838334321975708e-02 -3.0565607547760010e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 1.8905990291386843e-03</internalNodes>
          <leafValues>
            2.3341298103332520e-02 -6.6700172424316406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 4.9954187124967575e-03</internalNodes>
          <leafValues>
            2.5513354688882828e-02 -5.4635345935821533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 -5.5998284369707108e-03</internalNodes>
          <leafValues>
            2.9532432556152344e-01 -5.9350244700908661e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1008 -1.0907559189945459e-03</internalNodes>
          <leafValues>
            1.8265166878700256e-01 -9.8137028515338898e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 -7.4323470471426845e-04</internalNodes>
          <leafValues>
            1.9020494818687439e-01 -8.7386451661586761e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 2.7787161525338888e-03</internalNodes>
          <leafValues>
            3.2241951674222946e-02 -4.8055323958396912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 2.4344769772142172e-03</internalNodes>
          <leafValues>
            4.6477138996124268e-02 -2.9923307895660400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 2.8132982552051544e-03</internalNodes>
          <leafValues>
            -9.0026579797267914e-02 1.6738441586494446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 3.2191604375839233e-02</internalNodes>
          <leafValues>
            -6.3697919249534607e-02 2.8380525112152100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -1.8642821814864874e-03</internalNodes>
          <leafValues>
            2.0616722106933594e-01 -7.4722714722156525e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 4.0091956034302711e-03</internalNodes>
          <leafValues>
            -7.1015752851963043e-02 2.5589218735694885e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 -5.1108514890074730e-03</internalNodes>
          <leafValues>
            -4.8940917849540710e-01 3.4555420279502869e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 -1.9523575901985168e-02</internalNodes>
          <leafValues>
            3.1921747326850891e-01 -5.1439035683870316e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 -1.4431261457502842e-02</internalNodes>
          <leafValues>
            1.4213174581527710e-01 -1.1113181710243225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 4.5302580110728741e-04</internalNodes>
          <leafValues>
            -1.0926237702369690e-01 1.4363190531730652e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 -5.4108840413391590e-03</internalNodes>
          <leafValues>
            -4.6926099061965942e-01 3.1095381826162338e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 1.6963672824203968e-03</internalNodes>
          <leafValues>
            -6.7337587475776672e-02 2.2115154564380646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 1.8719944637268782e-03</internalNodes>
          <leafValues>
            -5.8433420956134796e-02 2.7830049395561218e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1014 -8.3780642598867416e-03</internalNodes>
          <leafValues>
            -4.6290600299835205e-01 3.3701810985803604e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 1.0720299184322357e-01</internalNodes>
          <leafValues>
            2.6600774377584457e-02 -5.0957643985748291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 -1.5523867914453149e-03</internalNodes>
          <leafValues>
            -5.7974040508270264e-01 2.2188233211636543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 -1.0537400841712952e-02</internalNodes>
          <leafValues>
            -4.3835061788558960e-01 2.9434528201818466e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1038 3.1337797641754150e-02</internalNodes>
          <leafValues>
            2.0445786416530609e-02 -6.3010692596435547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1004 -5.1124744117259979e-02</internalNodes>
          <leafValues>
            -6.7282766103744507e-01 1.8230145797133446e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 -6.0091790510341525e-04</internalNodes>
          <leafValues>
            2.0237097144126892e-01 -7.2557553648948669e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 1.6933252336457372e-03</internalNodes>
          <leafValues>
            -5.9000160545110703e-02 2.4010565876960754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 5.7134744711220264e-03</internalNodes>
          <leafValues>
            2.9386352747678757e-02 -5.1309728622436523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 -9.6922749653458595e-03</internalNodes>
          <leafValues>
            -5.4907989501953125e-01 2.3704739287495613e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 -1.2504560872912407e-02</internalNodes>
          <leafValues>
            -6.1863696575164795e-01 1.9876839593052864e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 -9.1812955215573311e-03</internalNodes>
          <leafValues>
            -4.7697570919990540e-01 2.5203671306371689e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 2.8069302439689636e-02</internalNodes>
          <leafValues>
            -5.5565606802701950e-02 2.5318285822868347e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 4.6324366703629494e-03</internalNodes>
          <leafValues>
            2.5273589417338371e-02 -5.9603255987167358e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 2.9409723356366158e-03</internalNodes>
          <leafValues>
            -5.1576137542724609e-02 2.9322555661201477e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 -1.6009721904993057e-02</internalNodes>
          <leafValues>
            2.9389014840126038e-01 -4.7874812036752701e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 -2.0468614995479584e-02</internalNodes>
          <leafValues>
            1.4383009076118469e-01 -1.0160042345523834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 2.3338340222835541e-02</internalNodes>
          <leafValues>
            -5.7301126420497894e-02 2.9121819138526917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 -2.1875634789466858e-02</internalNodes>
          <leafValues>
            -6.4106851816177368e-01 2.4203805252909660e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 1.1228370480239391e-02</internalNodes>
          <leafValues>
            -5.2143514156341553e-02 2.8465506434440613e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 -4.3659657239913940e-03</internalNodes>
          <leafValues>
            -6.0558545589447021e-01 2.5440702214837074e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 1.1577639961615205e-03</internalNodes>
          <leafValues>
            -8.9793093502521515e-02 1.6500258445739746e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 1.1090341955423355e-02</internalNodes>
          <leafValues>
            2.4472476914525032e-02 -6.1380225419998169e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1015 4.7660744749009609e-03</internalNodes>
          <leafValues>
            4.1726417839527130e-02 -3.2548862695693970e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 2.4865168597898446e-05</internalNodes>
          <leafValues>
            -1.2436556816101074e-01 1.1702288687229156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 -7.6379198580980301e-03</internalNodes>
          <leafValues>
            -4.9008071422576904e-01 2.9381709173321724e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 445 -3.2750256359577179e-03</internalNodes>
          <leafValues>
            1.7950019240379333e-01 -8.0592408776283264e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 1.3944536913186312e-03</internalNodes>
          <leafValues>
            -8.0001771450042725e-02 2.2785140573978424e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 1.9776031840592623e-03</internalNodes>
          <leafValues>
            3.4109916538000107e-02 -4.8504865169525146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 -3.9329148828983307e-02</internalNodes>
          <leafValues>
            -6.8790251016616821e-01 1.7370922490954399e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 -2.8447234071791172e-03</internalNodes>
          <leafValues>
            2.3028372228145599e-01 -6.6618286073207855e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 3.2375190407037735e-02</internalNodes>
          <leafValues>
            -7.5743824243545532e-02 1.7864570021629333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 5.1314428448677063e-02</internalNodes>
          <leafValues>
            -5.3142681717872620e-02 2.8643575310707092e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 4.6999715268611908e-03</internalNodes>
          <leafValues>
            3.5749543458223343e-02 -4.0437424182891846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 -2.0850417204201221e-03</internalNodes>
          <leafValues>
            -3.0815458297729492e-01 4.2763352394104004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 -9.1223767958581448e-04</internalNodes>
          <leafValues>
            2.1245715022087097e-01 -6.7729450762271881e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 -2.2479293693322688e-04</internalNodes>
          <leafValues>
            1.3159312307834625e-01 -1.0141336172819138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 974 3.1234124675393105e-02</internalNodes>
          <leafValues>
            -8.9100256562232971e-02 1.5734429657459259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -1.5079543227329850e-03</internalNodes>
          <leafValues>
            3.2412421703338623e-01 -4.4387526810169220e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 17 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.2500010728836060e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 803 -5.5631361901760101e-03</internalNodes>
          <leafValues>
            4.5343571901321411e-01 -5.2330773323774338e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 4.1911248117685318e-03</internalNodes>
          <leafValues>
            -1.2266161292791367e-01 3.6830583214759827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 -1.8559540621936321e-03</internalNodes>
          <leafValues>
            2.4044598639011383e-01 -1.5207393467426300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 -1.1846812441945076e-02</internalNodes>
          <leafValues>
            2.7016878128051758e-01 -1.1934488266706467e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 1.0401019826531410e-03</internalNodes>
          <leafValues>
            -2.3527304828166962e-01 9.5964968204498291e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 9.3873767182230949e-03</internalNodes>
          <leafValues>
            -5.6923847645521164e-02 4.2236638069152832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 9.0843521058559418e-02</internalNodes>
          <leafValues>
            -6.3625380396842957e-02 3.8295668363571167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 439 -1.6221515834331512e-03</internalNodes>
          <leafValues>
            1.8148291110992432e-01 -1.3424767553806305e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -1.8008962273597717e-02</internalNodes>
          <leafValues>
            2.7346464991569519e-01 -7.6283894479274750e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 8.6509017273783684e-03</internalNodes>
          <leafValues>
            5.8148156851530075e-02 -5.2620184421539307e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 2.8817038983106613e-03</internalNodes>
          <leafValues>
            2.6940831914544106e-02 -4.7911167144775391e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 -6.1017833650112152e-03</internalNodes>
          <leafValues>
            1.7878855764865875e-01 -1.2378337979316711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 -5.9294269885867834e-04</internalNodes>
          <leafValues>
            -2.7179723978042603e-01 8.0951526761054993e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 996 3.1696190126240253e-04</internalNodes>
          <leafValues>
            -1.7311862111091614e-01 1.0296358913183212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 6.6280784085392952e-03</internalNodes>
          <leafValues>
            -5.8870136737823486e-02 2.9477587342262268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 -4.5112203806638718e-03</internalNodes>
          <leafValues>
            -5.9672296047210693e-01 2.7053238824009895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -4.3381296098232269e-02</internalNodes>
          <leafValues>
            -4.2040801048278809e-01 4.0890187025070190e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 2.0323593635112047e-03</internalNodes>
          <leafValues>
            5.5178079754114151e-02 -3.0439695715904236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 973 1.8127080984413624e-03</internalNodes>
          <leafValues>
            -8.2048252224922180e-02 2.1907366812229156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 -6.6424394026398659e-03</internalNodes>
          <leafValues>
            -4.7840338945388794e-01 4.4878169894218445e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 -8.5755460895597935e-04</internalNodes>
          <leafValues>
            1.3301849365234375e-01 -1.2699788808822632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 3.4769098274409771e-03</internalNodes>
          <leafValues>
            -7.1578972041606903e-02 2.5448271632194519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 -1.8520625308156013e-03</internalNodes>
          <leafValues>
            1.5127970278263092e-01 -1.2349219620227814e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 777 5.4582338780164719e-03</internalNodes>
          <leafValues>
            3.5001352429389954e-02 -4.8021456599235535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 -6.4206691458821297e-03</internalNodes>
          <leafValues>
            -5.6509351730346680e-01 2.6883032172918320e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 8.2498416304588318e-03</internalNodes>
          <leafValues>
            4.3442543596029282e-02 -3.7965279817581177e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 3.0813394114375114e-03</internalNodes>
          <leafValues>
            -5.6544844061136246e-02 3.2101437449455261e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 865 2.8121876530349255e-03</internalNodes>
          <leafValues>
            -7.1444042026996613e-02 2.8035575151443481e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 -1.1791236698627472e-02</internalNodes>
          <leafValues>
            2.0067863166332245e-01 -1.0047248005867004e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 1.4931729529052973e-03</internalNodes>
          <leafValues>
            -6.6428750753402710e-02 2.6187655329704285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 -2.8772680088877678e-03</internalNodes>
          <leafValues>
            -4.5838123559951782e-01 4.2477916926145554e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 -4.5857336372137070e-03</internalNodes>
          <leafValues>
            1.2718579173088074e-01 -1.3642288744449615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 -1.3770985417068005e-02</internalNodes>
          <leafValues>
            -6.4000308513641357e-01 2.7297915890812874e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 -3.6472730338573456e-02</internalNodes>
          <leafValues>
            -5.1465278863906860e-01 3.1265191733837128e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 1.0626764036715031e-02</internalNodes>
          <leafValues>
            2.4199636653065681e-02 -6.3441967964172363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 -3.6817211657762527e-03</internalNodes>
          <leafValues>
            -4.4575414061546326e-01 3.1119547784328461e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 -3.4752404317259789e-03</internalNodes>
          <leafValues>
            1.4008119702339172e-01 -1.0539831966161728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 -4.7973562031984329e-03</internalNodes>
          <leafValues>
            2.8762820363044739e-01 -6.0662355273962021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 6.4153699204325676e-03</internalNodes>
          <leafValues>
            -1.1230263859033585e-01 1.4087037742137909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 -1.0156400967389345e-03</internalNodes>
          <leafValues>
            -3.3441004157066345e-01 4.3477565050125122e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 3.3057793043553829e-03</internalNodes>
          <leafValues>
            1.9609324634075165e-02 -7.0060092210769653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -5.3275022655725479e-03</internalNodes>
          <leafValues>
            2.4580952525138855e-01 -6.0118518769741058e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 1.5886269975453615e-03</internalNodes>
          <leafValues>
            -7.7446170151233673e-02 1.9878011941909790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 4.7287968918681145e-03</internalNodes>
          <leafValues>
            3.0098341405391693e-02 -5.0950014591217041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 -1.9788878853432834e-04</internalNodes>
          <leafValues>
            1.5142950415611267e-01 -9.6688762307167053e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 -4.9208370037376881e-03</internalNodes>
          <leafValues>
            -4.5343187451362610e-01 3.7627156823873520e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 4.5094583183526993e-02</internalNodes>
          <leafValues>
            -8.5510566830635071e-02 1.7849470674991608e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 1.4799998607486486e-03</internalNodes>
          <leafValues>
            -6.4638271927833557e-02 2.3496921360492706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 517 1.0061380267143250e-01</internalNodes>
          <leafValues>
            -3.0139762908220291e-02 4.9012109637260437e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -5.2844230085611343e-03</internalNodes>
          <leafValues>
            1.7104546725749969e-01 -8.7710574269294739e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 -8.3214940968900919e-04</internalNodes>
          <leafValues>
            -2.6654696464538574e-01 5.3875535726547241e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 -8.8889291509985924e-04</internalNodes>
          <leafValues>
            1.8824113905429840e-01 -8.0119885504245758e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 2.2177316714078188e-03</internalNodes>
          <leafValues>
            -6.9703146815299988e-02 2.0391084253787994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -1.1522162239998579e-03</internalNodes>
          <leafValues>
            -3.6508113145828247e-01 3.9048090577125549e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1036 -1.0836161673069000e-02</internalNodes>
          <leafValues>
            -5.8106678724288940e-01 2.1713526919484138e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 -1.6731536388397217e-01</internalNodes>
          <leafValues>
            -4.7344669699668884e-01 2.6662701740860939e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 -9.5267388969659805e-03</internalNodes>
          <leafValues>
            2.7732986211776733e-01 -5.6512769311666489e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 6.6450019367039204e-03</internalNodes>
          <leafValues>
            2.9381312429904938e-02 -5.3565382957458496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -2.1554589271545410e-02</internalNodes>
          <leafValues>
            -6.2839144468307495e-01 1.8782904371619225e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 1.4288825332187116e-04</internalNodes>
          <leafValues>
            -1.2763719260692596e-01 1.0616952925920486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 1.8068919889628887e-03</internalNodes>
          <leafValues>
            4.2757544666528702e-02 -3.2102146744728088e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 1.2280542869120836e-03</internalNodes>
          <leafValues>
            -5.7478122413158417e-02 2.5948432087898254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 2.6250675320625305e-02</internalNodes>
          <leafValues>
            -9.5928788185119629e-02 1.4502045512199402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 1.8192850984632969e-03</internalNodes>
          <leafValues>
            -6.8028703331947327e-02 2.3167446255683899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -4.8545510508120060e-03</internalNodes>
          <leafValues>
            -4.3374514579772949e-01 3.6196250468492508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 2.8766903560608625e-03</internalNodes>
          <leafValues>
            3.8431353867053986e-02 -3.3900904655456543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 4.4511677697300911e-03</internalNodes>
          <leafValues>
            -4.8704307526350021e-02 2.9764902591705322e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 -9.9098179489374161e-03</internalNodes>
          <leafValues>
            2.5863200426101685e-01 -5.7418409734964371e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -2.6503708213567734e-03</internalNodes>
          <leafValues>
            1.3571591675281525e-01 -1.1608450859785080e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -3.0543167144060135e-02</internalNodes>
          <leafValues>
            2.8910955786705017e-01 -5.1689133048057556e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 -2.6757145300507545e-02</internalNodes>
          <leafValues>
            1.8446540832519531e-01 -7.7666454017162323e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 -2.2985447198152542e-02</internalNodes>
          <leafValues>
            -3.5471677780151367e-01 4.1345477104187012e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 9.5467511564493179e-03</internalNodes>
          <leafValues>
            -5.5719308555126190e-02 2.4589607119560242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 2.6181992143392563e-03</internalNodes>
          <leafValues>
            -1.0256808251142502e-01 1.3319683074951172e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1031 -3.5491142421960831e-02</internalNodes>
          <leafValues>
            -5.9519535303115845e-01 2.2935084998607635e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 1.5474080573767424e-03</internalNodes>
          <leafValues>
            -8.4649838507175446e-02 1.6198579967021942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 -3.4878745209425688e-03</internalNodes>
          <leafValues>
            -5.0121647119522095e-01 2.6359066367149353e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 3.6612942349165678e-03</internalNodes>
          <leafValues>
            -7.2178244590759277e-02 1.8415448069572449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -2.1762652322649956e-03</internalNodes>
          <leafValues>
            2.1102276444435120e-01 -6.4692504703998566e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 -6.9864131510257721e-03</internalNodes>
          <leafValues>
            -4.3104550242424011e-01 3.3448409289121628e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 4.7067347913980484e-03</internalNodes>
          <leafValues>
            4.7681909054517746e-02 -3.1132212281227112e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1054 -7.0012239739298820e-03</internalNodes>
          <leafValues>
            -3.4665238857269287e-01 3.6263268440961838e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 1.0144514963030815e-02</internalNodes>
          <leafValues>
            3.3140499144792557e-02 -3.7149414420127869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 2.5893552228808403e-03</internalNodes>
          <leafValues>
            -5.6186988949775696e-02 2.3859155178070068e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 -3.8091647438704967e-03</internalNodes>
          <leafValues>
            1.8803173303604126e-01 -9.0667806565761566e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 -2.5004068017005920e-01</internalNodes>
          <leafValues>
            -5.7437247037887573e-01 2.3015361279249191e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 -8.5459719412028790e-04</internalNodes>
          <leafValues>
            -3.0019384622573853e-01 4.1898671537637711e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -1.5604835003614426e-02</internalNodes>
          <leafValues>
            -5.8520871400833130e-01 2.1410541608929634e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 -1.9794562458992004e-01</internalNodes>
          <leafValues>
            -6.7963910102844238e-01 1.6488522291183472e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 -1.9824346527457237e-03</internalNodes>
          <leafValues>
            1.4493939280509949e-01 -8.7999224662780762e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 -2.1158650517463684e-02</internalNodes>
          <leafValues>
            -6.4664304256439209e-01 2.4590896442532539e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -9.3553803162649274e-04</internalNodes>
          <leafValues>
            1.8229192495346069e-01 -7.2682343423366547e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 -1.1120189446955919e-03</internalNodes>
          <leafValues>
            1.5188181400299072e-01 -8.6225852370262146e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 1.1543033272027969e-01</internalNodes>
          <leafValues>
            -4.7091111540794373e-02 3.5574361681938171e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 -5.2959467284381390e-03</internalNodes>
          <leafValues>
            2.0496748387813568e-01 -6.1289250850677490e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 -2.6194794103503227e-02</internalNodes>
          <leafValues>
            1.7320305109024048e-01 -1.1094193905591965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 1.4183738268911839e-02</internalNodes>
          <leafValues>
            -9.7011148929595947e-02 1.4372280240058899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1032 -3.6340979859232903e-03</internalNodes>
          <leafValues>
            -4.0951785445213318e-01 3.0991807579994202e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1028 1.4448106288909912e-02</internalNodes>
          <leafValues>
            -6.1627220362424850e-02 2.0916682481765747e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 982 -1.1399465613067150e-02</internalNodes>
          <leafValues>
            1.8926219642162323e-01 -8.7004892528057098e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 18 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.2953979969024658e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 725 1.6048721969127655e-02</internalNodes>
          <leafValues>
            -9.5187164843082428e-02 3.7635341286659241e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 4.1785854846239090e-03</internalNodes>
          <leafValues>
            -1.4184002578258514e-01 3.1887301802635193e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 -6.7659835331141949e-03</internalNodes>
          <leafValues>
            3.7005490064620972e-01 -8.9318118989467621e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 1.4478694647550583e-02</internalNodes>
          <leafValues>
            -1.3418816030025482e-01 2.8370034694671631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -1.8653089646250010e-03</internalNodes>
          <leafValues>
            -3.5015934705734253e-01 6.9187328219413757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 3.7634610198438168e-03</internalNodes>
          <leafValues>
            -7.7612839639186859e-02 3.0384179949760437e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 8.9913085103034973e-03</internalNodes>
          <leafValues>
            6.0584690421819687e-02 -4.7271341085433960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -3.0867164023220539e-03</internalNodes>
          <leafValues>
            1.6870087385177612e-01 -1.3231597840785980e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 -4.0246914140880108e-03</internalNodes>
          <leafValues>
            -4.1840493679046631e-01 6.4627721905708313e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 4.8679644241929054e-03</internalNodes>
          <leafValues>
            -5.6233335286378860e-02 4.2156839370727539e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 5.5472417734563351e-03</internalNodes>
          <leafValues>
            3.7891130894422531e-02 -5.1408857107162476e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1003 6.5884483046829700e-04</internalNodes>
          <leafValues>
            -1.6457377374172211e-01 1.1204792559146881e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1050 -1.0980388615280390e-03</internalNodes>
          <leafValues>
            -3.3544427156448364e-01 4.6025454998016357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 -2.8328509069979191e-03</internalNodes>
          <leafValues>
            2.3426958918571472e-01 -7.2758100926876068e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 1.5504788607358932e-03</internalNodes>
          <leafValues>
            6.2664858996868134e-02 -2.5632002949714661e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 -6.2153179896995425e-04</internalNodes>
          <leafValues>
            1.7485393583774567e-01 -9.9982917308807373e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 -1.4540781266987324e-02</internalNodes>
          <leafValues>
            -4.4969236850738525e-01 3.7324137985706329e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 -1.6624422278255224e-03</internalNodes>
          <leafValues>
            1.4047256112098694e-01 -1.1892398446798325e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 1.6246617306023836e-03</internalNodes>
          <leafValues>
            6.1172962188720703e-02 -2.7449882030487061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 -1.1364535987377167e-01</internalNodes>
          <leafValues>
            -4.3175131082534790e-01 3.8861453533172607e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 6.3355863094329834e-03</internalNodes>
          <leafValues>
            4.3615639209747314e-02 -3.7530297040939331e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 -7.9950205981731415e-03</internalNodes>
          <leafValues>
            -5.6157833337783813e-01 2.7148496359586716e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -6.0972268693149090e-03</internalNodes>
          <leafValues>
            4.7499263286590576e-01 -3.5678520798683167e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 1.3845593202859163e-03</internalNodes>
          <leafValues>
            -1.1575383692979813e-01 1.3405258953571320e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 8.5432223975658417e-02</internalNodes>
          <leafValues>
            -5.6930482387542725e-02 3.1373351812362671e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 -1.2029780447483063e-01</internalNodes>
          <leafValues>
            -4.7989824414253235e-01 3.8594469428062439e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 -8.3766942843794823e-03</internalNodes>
          <leafValues>
            -2.0806340873241425e-01 7.6934777200222015e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 -4.6590538695454597e-03</internalNodes>
          <leafValues>
            -5.0349289178848267e-01 3.0419014394283295e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 -3.2761119306087494e-02</internalNodes>
          <leafValues>
            3.2354715466499329e-01 -5.6276485323905945e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 8.3009023219347000e-03</internalNodes>
          <leafValues>
            -8.3831317722797394e-02 2.3335608839988708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 5.7156109251081944e-03</internalNodes>
          <leafValues>
            -8.6484365165233612e-02 1.8363620340824127e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 -1.0080671310424805e-01</internalNodes>
          <leafValues>
            3.8774350285530090e-01 -4.0828518569469452e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -2.5552421808242798e-02</internalNodes>
          <leafValues>
            -5.0166463851928711e-01 3.8269419223070145e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -6.1748407781124115e-02</internalNodes>
          <leafValues>
            -3.5811841487884521e-01 4.6544160693883896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 -1.2269845232367516e-02</internalNodes>
          <leafValues>
            2.0786920189857483e-01 -7.8518457710742950e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 2.8048269450664520e-02</internalNodes>
          <leafValues>
            -5.6248739361763000e-02 2.8977242112159729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 -7.2269486263394356e-03</internalNodes>
          <leafValues>
            -7.2842431068420410e-01 2.3379294201731682e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 4.7771912068128586e-03</internalNodes>
          <leafValues>
            2.3226773366332054e-02 -5.6412339210510254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 2.8181755915284157e-03</internalNodes>
          <leafValues>
            -3.3893339335918427e-02 4.3989458680152893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 -8.4437360055744648e-04</internalNodes>
          <leafValues>
            1.9623728096485138e-01 -7.8485630452632904e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 -4.3037505820393562e-03</internalNodes>
          <leafValues>
            -3.6311796307563782e-01 4.0526941418647766e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 4.9789976328611374e-03</internalNodes>
          <leafValues>
            4.8658054322004318e-02 -3.1162264943122864e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1041 -5.0353109836578369e-03</internalNodes>
          <leafValues>
            -5.5396872758865356e-01 2.3420164361596107e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -1.3716940302401781e-03</internalNodes>
          <leafValues>
            2.2532704472541809e-01 -6.2741614878177643e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 3.3456790260970592e-03</internalNodes>
          <leafValues>
            3.8516163825988770e-02 -3.6224716901779175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 1.9023896893486381e-03</internalNodes>
          <leafValues>
            -5.4677281528711319e-02 2.5294607877731323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1037 -1.4274399727582932e-03</internalNodes>
          <leafValues>
            -3.7934723496437073e-01 3.8707002997398376e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 1.1010284069925547e-03</internalNodes>
          <leafValues>
            -9.5659099519252777e-02 1.4958517253398895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 -4.4154529459774494e-03</internalNodes>
          <leafValues>
            -5.1156622171401978e-01 2.5640288367867470e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 3.7023271434009075e-03</internalNodes>
          <leafValues>
            -4.3221119791269302e-02 3.2581970095634460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 -5.4480084218084812e-03</internalNodes>
          <leafValues>
            -4.7611567378044128e-01 3.5773757845163345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 -3.1974539160728455e-04</internalNodes>
          <leafValues>
            1.1916244029998779e-01 -1.1832383275032043e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 -2.8494147583842278e-02</internalNodes>
          <leafValues>
            -6.5004557371139526e-01 2.0599177107214928e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 -2.7449331246316433e-03</internalNodes>
          <leafValues>
            -3.9275056123733521e-01 3.3223718404769897e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 4.1362000629305840e-03</internalNodes>
          <leafValues>
            2.7191400527954102e-02 -4.7952741384506226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 3.3568721264600754e-03</internalNodes>
          <leafValues>
            -6.0983922332525253e-02 2.2964073717594147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 -5.7129040360450745e-03</internalNodes>
          <leafValues>
            -5.9052920341491699e-01 2.3388050496578217e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 -1.1567326728254557e-03</internalNodes>
          <leafValues>
            1.5093772113323212e-01 -9.1553181409835815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 -8.9379055425524712e-03</internalNodes>
          <leafValues>
            -3.5481104254722595e-01 3.6294396966695786e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 3.6097350530326366e-03</internalNodes>
          <leafValues>
            3.2780081033706665e-02 -3.8517734408378601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 2.0727193914353848e-03</internalNodes>
          <leafValues>
            -5.3627125918865204e-02 2.5666573643684387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 977 -1.8177125602960587e-03</internalNodes>
          <leafValues>
            2.0363596081733704e-01 -7.0555560290813446e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 -3.3223466016352177e-03</internalNodes>
          <leafValues>
            -4.8926571011543274e-01 2.8675178065896034e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 -4.4222660362720490e-03</internalNodes>
          <leafValues>
            -4.0920063853263855e-01 3.0863059684634209e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 -7.8024319373071194e-04</internalNodes>
          <leafValues>
            1.2166435271501541e-01 -1.0897941887378693e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 7.9855127260088921e-03</internalNodes>
          <leafValues>
            2.5865448638796806e-02 -4.8917418718338013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 -2.7752606911235489e-05</internalNodes>
          <leafValues>
            1.1611134558916092e-01 -1.1225233227014542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 3.0770362354815006e-03</internalNodes>
          <leafValues>
            -6.4753420650959015e-02 1.9632078707218170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 -2.1007210016250610e-03</internalNodes>
          <leafValues>
            1.9681814312934875e-01 -9.4167068600654602e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -6.1383144930005074e-03</internalNodes>
          <leafValues>
            -3.9225277304649353e-01 3.5275831818580627e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 1.1184177361428738e-02</internalNodes>
          <leafValues>
            2.9410628601908684e-02 -4.3673589825630188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1007 1.0432782582938671e-03</internalNodes>
          <leafValues>
            -6.7393802106380463e-02 1.9237922132015228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 8.5366604616865516e-04</internalNodes>
          <leafValues>
            -8.4067851305007935e-02 1.6720806062221527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -3.3059090375900269e-02</internalNodes>
          <leafValues>
            2.6451063156127930e-01 -5.2662543952465057e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 -8.7435375899076462e-03</internalNodes>
          <leafValues>
            -3.0780994892120361e-01 4.8419766128063202e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 -1.1587596964091063e-03</internalNodes>
          <leafValues>
            1.4863640069961548e-01 -9.4251774251461029e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 -2.2717786952853203e-02</internalNodes>
          <leafValues>
            -4.2414310574531555e-01 3.5150803625583649e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 -8.4660220891237259e-03</internalNodes>
          <leafValues>
            2.5765278935432434e-01 -5.4796367883682251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 -1.4943551504984498e-03</internalNodes>
          <leafValues>
            -2.7729934453964233e-01 4.9375709146261215e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -7.5480109080672264e-04</internalNodes>
          <leafValues>
            1.2197802960872650e-01 -1.0845532268285751e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 2.9903287068009377e-03</internalNodes>
          <leafValues>
            -8.4785357117652893e-02 1.5424512326717377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1040 1.7600806895643473e-03</internalNodes>
          <leafValues>
            7.0044547319412231e-02 -1.9795240461826324e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 1.2243577279150486e-02</internalNodes>
          <leafValues>
            -7.8472696244716644e-02 1.7095038294792175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -2.7739753946661949e-02</internalNodes>
          <leafValues>
            2.0475350320339203e-01 -6.9862313568592072e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 -6.4486754126846790e-03</internalNodes>
          <leafValues>
            -3.7651637196540833e-01 3.3540505915880203e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 -1.3427068479359150e-02</internalNodes>
          <leafValues>
            1.5320046246051788e-01 -8.3272159099578857e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 8.2654636353254318e-03</internalNodes>
          <leafValues>
            -8.1395141780376434e-02 1.9696740806102753e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 3.0615129508078098e-03</internalNodes>
          <leafValues>
            -5.8534789830446243e-02 2.1799990534782410e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 -1.4359520282596350e-03</internalNodes>
          <leafValues>
            1.8553669750690460e-01 -7.9428143799304962e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 2.8793164528906345e-03</internalNodes>
          <leafValues>
            3.7499722093343735e-02 -3.5483118891716003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 -9.0899681672453880e-03</internalNodes>
          <leafValues>
            -5.9031629562377930e-01 2.0012531429529190e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 1.6797243151813745e-03</internalNodes>
          <leafValues>
            -6.8868115544319153e-02 1.8992543220520020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 -1.1759581044316292e-02</internalNodes>
          <leafValues>
            3.6288693547248840e-01 -3.3578243106603622e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 3.8305222988128662e-03</internalNodes>
          <leafValues>
            -6.6793553531169891e-02 1.9304293394088745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1018 1.2506111524999142e-03</internalNodes>
          <leafValues>
            -8.1618689000606537e-02 1.5481384098529816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 -1.6119323670864105e-02</internalNodes>
          <leafValues>
            1.4024992287158966e-01 -9.3965478241443634e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 -7.2789913974702358e-04</internalNodes>
          <leafValues>
            1.9554650783538818e-01 -7.2329640388488770e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 1.4888901496306062e-03</internalNodes>
          <leafValues>
            3.3372651785612106e-02 -4.0691211819648743e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 984 -4.9822013825178146e-03</internalNodes>
          <leafValues>
            -3.3125448226928711e-01 3.6899805068969727e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1053 9.4443336129188538e-03</internalNodes>
          <leafValues>
            3.1763385981321335e-02 -3.7651473283767700e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 19 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.3101767301559448e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 535 -1.2652185745537281e-02</internalNodes>
          <leafValues>
            4.0350878238677979e-01 -8.6829073727130890e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 4.8778904601931572e-03</internalNodes>
          <leafValues>
            -9.1208808124065399e-02 4.8882400989532471e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -2.4099014699459076e-02</internalNodes>
          <leafValues>
            3.6089360713958740e-01 -1.1495783179998398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 1.7244052141904831e-03</internalNodes>
          <leafValues>
            -1.5974776446819305e-01 1.6197346150875092e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -3.6334272008389235e-03</internalNodes>
          <leafValues>
            2.7575418353080750e-01 -9.4314105808734894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 -3.4076566807925701e-03</internalNodes>
          <leafValues>
            2.2806543111801147e-01 -1.1266379803419113e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 8.8951038196682930e-03</internalNodes>
          <leafValues>
            -6.6720969974994659e-02 3.3090111613273621e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -2.4365000426769257e-03</internalNodes>
          <leafValues>
            -4.6264356374740601e-01 5.9559248387813568e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 1.6330357640981674e-02</internalNodes>
          <leafValues>
            6.1187297105789185e-02 -4.2252638936042786e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 8.4438512567430735e-04</internalNodes>
          <leafValues>
            -1.6640183329582214e-01 1.1608948558568954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 2.9493896290659904e-03</internalNodes>
          <leafValues>
            -9.1952294111251831e-02 2.0670032501220703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 3.4696407616138458e-02</internalNodes>
          <leafValues>
            -8.0334044992923737e-02 2.8779104351997375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 -3.3343117684125900e-03</internalNodes>
          <leafValues>
            -5.9474521875381470e-01 3.6547001451253891e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 9.3975086929276586e-04</internalNodes>
          <leafValues>
            -1.5703736245632172e-01 1.1884722858667374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 -3.4337402321398258e-03</internalNodes>
          <leafValues>
            -5.6122291088104248e-01 3.2535579055547714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1010 2.6463428512215614e-03</internalNodes>
          <leafValues>
            -7.0756055414676666e-02 2.5195503234863281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 -5.4167490452528000e-04</internalNodes>
          <leafValues>
            1.2782673537731171e-01 -1.3642209768295288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 2.6469756849110126e-03</internalNodes>
          <leafValues>
            4.3448049575090408e-02 -4.2012536525726318e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -3.8945327978581190e-03</internalNodes>
          <leafValues>
            -3.4613665938377380e-01 4.6863511204719543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 1.0849055834114552e-03</internalNodes>
          <leafValues>
            -7.2841711342334747e-02 2.2674085199832916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 -9.8655023612082005e-04</internalNodes>
          <leafValues>
            2.5967630743980408e-01 -8.0196425318717957e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 4.3801497668027878e-03</internalNodes>
          <leafValues>
            2.8548270463943481e-02 -6.2486541271209717e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 3.1944573856890202e-04</internalNodes>
          <leafValues>
            -1.4062304794788361e-01 1.1761485785245895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 6.6440929658710957e-03</internalNodes>
          <leafValues>
            3.2654736191034317e-02 -4.6211913228034973e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 7.0357543881982565e-04</internalNodes>
          <leafValues>
            7.5751155614852905e-02 -1.9804775714874268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 5.4024737328290939e-03</internalNodes>
          <leafValues>
            -6.1951220035552979e-02 2.4502439796924591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 7.2796619497239590e-03</internalNodes>
          <leafValues>
            -5.9379905462265015e-02 2.5588110089302063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 -1.5059831552207470e-02</internalNodes>
          <leafValues>
            -6.6548824310302734e-01 2.2492453455924988e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 -4.6248016878962517e-03</internalNodes>
          <leafValues>
            -3.4483894705772400e-01 4.2247168719768524e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 1.4736279845237732e-03</internalNodes>
          <leafValues>
            3.3624436706304550e-02 -4.1066497564315796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 4.0667224675416946e-03</internalNodes>
          <leafValues>
            -8.6238399147987366e-02 1.6550070047378540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 -1.2728295987471938e-03</internalNodes>
          <leafValues>
            1.9737298786640167e-01 -9.5425128936767578e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 -1.5297440811991692e-02</internalNodes>
          <leafValues>
            -5.9287589788436890e-01 2.3890895769000053e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 -2.9415758326649666e-03</internalNodes>
          <leafValues>
            -4.8744291067123413e-01 2.8945079073309898e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 840 9.3173712957650423e-04</internalNodes>
          <leafValues>
            -8.9065223932266235e-02 1.6721877455711365e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 2.1161064505577087e-03</internalNodes>
          <leafValues>
            -5.8501452207565308e-02 2.7767315506935120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 -3.7564497906714678e-03</internalNodes>
          <leafValues>
            2.6502594351768494e-01 -5.3400754928588867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 1.9215289503335953e-02</internalNodes>
          <leafValues>
            3.6197379231452942e-02 -3.9996260404586792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -5.8480387087911367e-04</internalNodes>
          <leafValues>
            1.7670612037181854e-01 -8.0434471368789673e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 1.7193648964166641e-02</internalNodes>
          <leafValues>
            2.1810308098793030e-02 -6.6349571943283081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 -1.5182361006736755e-02</internalNodes>
          <leafValues>
            2.4825552105903625e-01 -6.3092373311519623e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 3.0793007463216782e-03</internalNodes>
          <leafValues>
            2.4977168068289757e-02 -5.3303867578506470e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 -2.4421955458819866e-03</internalNodes>
          <leafValues>
            -3.6828973889350891e-01 3.3543743193149567e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1011 7.0760864764451981e-04</internalNodes>
          <leafValues>
            -7.0839107036590576e-02 1.9299270212650299e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -2.9198618140071630e-03</internalNodes>
          <leafValues>
            -4.2773759365081787e-01 3.4788779914379120e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 4.9937088042497635e-03</internalNodes>
          <leafValues>
            3.5642433911561966e-02 -3.7421676516532898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 3.1980490311980247e-03</internalNodes>
          <leafValues>
            -6.5103210508823395e-02 2.1381905674934387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 -1.1253832839429379e-02</internalNodes>
          <leafValues>
            1.9790579378604889e-01 -7.1859836578369141e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 -3.6279223859310150e-02</internalNodes>
          <leafValues>
            1.7960831522941589e-01 -9.7373597323894501e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 2.5160997174680233e-03</internalNodes>
          <leafValues>
            4.7910790890455246e-02 -2.7035105228424072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 1.2429051566869020e-03</internalNodes>
          <leafValues>
            -7.8723609447479248e-02 1.7209371924400330e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 -1.6120750457048416e-02</internalNodes>
          <leafValues>
            2.6868200302124023e-01 -5.0688084214925766e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 1.9487962126731873e-03</internalNodes>
          <leafValues>
            4.2773328721523285e-02 -3.2401460409164429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 7.1887858211994171e-04</internalNodes>
          <leafValues>
            -9.3979224562644958e-02 1.4450067281723022e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 2.4896476417779922e-02</internalNodes>
          <leafValues>
            3.0655095353722572e-02 -4.5330229401588440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1026 -3.9382722228765488e-02</internalNodes>
          <leafValues>
            -7.5473642349243164e-01 1.4460344798862934e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 1.6916246712207794e-01</internalNodes>
          <leafValues>
            1.8219815567135811e-02 -6.0212779045104980e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 2.6912155590252951e-05</internalNodes>
          <leafValues>
            -1.3110430538654327e-01 1.0080647468566895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 -1.1350987479090691e-03</internalNodes>
          <leafValues>
            -3.5285457968711853e-01 3.5424951463937759e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -5.3854554425925016e-04</internalNodes>
          <leafValues>
            1.6519539058208466e-01 -8.5205554962158203e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1006 -7.9703063238412142e-04</internalNodes>
          <leafValues>
            1.2170238047838211e-01 -1.1191177368164062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1055 6.4357938244938850e-03</internalNodes>
          <leafValues>
            2.3892326280474663e-02 -5.2907115221023560e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 3.5384115763008595e-03</internalNodes>
          <leafValues>
            1.5895446762442589e-02 -7.3063355684280396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -5.9715351089835167e-03</internalNodes>
          <leafValues>
            -4.9897637963294983e-01 2.2720154374837875e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 -1.3486531376838684e-01</internalNodes>
          <leafValues>
            4.7622504830360413e-01 -3.0212458223104477e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 1.5813487116247416e-03</internalNodes>
          <leafValues>
            -6.4366899430751801e-02 1.9106543064117432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 1.2239011703059077e-03</internalNodes>
          <leafValues>
            3.5654775798320770e-02 -3.6865225434303284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 1.5586249064654112e-03</internalNodes>
          <leafValues>
            -7.6894849538803101e-02 1.7627324163913727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 8.1224087625741959e-03</internalNodes>
          <leafValues>
            -9.0349502861499786e-02 1.4695085585117340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 -1.1717316228896379e-03</internalNodes>
          <leafValues>
            -4.2172068357467651e-01 3.2626960426568985e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 3.1573872547596693e-03</internalNodes>
          <leafValues>
            1.6080003231763840e-02 -7.3708915710449219e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 -6.0417165514081717e-04</internalNodes>
          <leafValues>
            1.3188406825065613e-01 -1.0221557319164276e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 5.9989960864186287e-03</internalNodes>
          <leafValues>
            -5.6194521486759186e-02 2.4262723326683044e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 9.2063043266534805e-03</internalNodes>
          <leafValues>
            -7.4052155017852783e-02 1.9847218692302704e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 5.9181386604905128e-03</internalNodes>
          <leafValues>
            2.7928760275244713e-02 -5.3380137681961060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 2.2121241781860590e-03</internalNodes>
          <leafValues>
            -7.4788182973861694e-02 1.9799898564815521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 1.5453733503818512e-03</internalNodes>
          <leafValues>
            -8.1615962088108063e-02 1.7845135927200317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 -2.7309993747621775e-03</internalNodes>
          <leafValues>
            -2.9415401816368103e-01 4.8099983483552933e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 1.5755122527480125e-02</internalNodes>
          <leafValues>
            -8.2719191908836365e-02 1.5387716889381409e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 -5.5120363831520081e-02</internalNodes>
          <leafValues>
            -2.7076271176338196e-01 5.2753895521163940e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 2.9593750834465027e-01</internalNodes>
          <leafValues>
            -2.5313137099146843e-02 5.3404790163040161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 -1.1218986473977566e-03</internalNodes>
          <leafValues>
            1.1400944739580154e-01 -1.1270149052143097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 -3.7802509963512421e-02</internalNodes>
          <leafValues>
            3.1571185588836670e-01 -4.9672659486532211e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 7.6384171843528748e-03</internalNodes>
          <leafValues>
            -1.0544487833976746e-01 1.6579298675060272e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 6.8679507821798325e-03</internalNodes>
          <leafValues>
            -6.0160953551530838e-02 2.2640766203403473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 5.1510091871023178e-02</internalNodes>
          <leafValues>
            2.6919802650809288e-02 -5.1188707351684570e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 997 -1.7317479476332664e-02</internalNodes>
          <leafValues>
            2.8218811750411987e-01 -4.4739942997694016e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 8.3876429125666618e-03</internalNodes>
          <leafValues>
            -5.7016383856534958e-02 2.2617760300636292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 9.2909142374992371e-02</internalNodes>
          <leafValues>
            3.1283479183912277e-02 -4.9390810728073120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 4.8232711851596832e-03</internalNodes>
          <leafValues>
            2.4896934628486633e-02 -4.5571261644363403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 2.3969253525137901e-03</internalNodes>
          <leafValues>
            2.3365976288914680e-02 -4.8319596052169800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 -3.8546645082533360e-03</internalNodes>
          <leafValues>
            2.0274488627910614e-01 -5.8264043182134628e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 -1.2048919452354312e-03</internalNodes>
          <leafValues>
            -3.4361392259597778e-01 3.4746967256069183e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 -1.6053356230258942e-02</internalNodes>
          <leafValues>
            1.8685258924961090e-01 -6.7979305982589722e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1045 -2.1703056991100311e-02</internalNodes>
          <leafValues>
            -5.0804340839385986e-01 2.5113353505730629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 -1.9719875417649746e-03</internalNodes>
          <leafValues>
            -2.7325069904327393e-01 4.3638698756694794e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -1.3189280871301889e-03</internalNodes>
          <leafValues>
            2.5198838114738464e-01 -4.8170279711484909e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 1.3257672544568777e-03</internalNodes>
          <leafValues>
            -6.6290155053138733e-02 2.6572498679161072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1024 -2.5993511080741882e-03</internalNodes>
          <leafValues>
            -7.1209841966629028e-01 1.9255550578236580e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 4.0416182018816471e-03</internalNodes>
          <leafValues>
            2.4820772930979729e-02 -4.3810126185417175e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          0 0 2 4 -1.</_>
        <_>
          0 2 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 6 14 -1.</_>
        <_>
          0 0 3 7 2.</_>
        <_>
          3 7 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 1 -1.</_>
        <_>
          4 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 2 -1.</_>
        <_>
          4 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 6 -1.</_>
        <_>
          0 0 4 3 2.</_>
        <_>
          4 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 12 -1.</_>
        <_>
          0 0 4 6 2.</_>
        <_>
          4 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 14 -1.</_>
        <_>
          0 0 4 7 2.</_>
        <_>
          4 7 4 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 6 -1.</_>
        <_>
          0 0 5 3 2.</_>
        <_>
          5 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 8 -1.</_>
        <_>
          0 0 5 4 2.</_>
        <_>
          5 4 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 18 13 -1.</_>
        <_>
          6 0 6 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 14 10 -1.</_>
        <_>
          0 0 7 5 2.</_>
        <_>
          7 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          8 0 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 6 -1.</_>
        <_>
          0 0 8 3 2.</_>
        <_>
          8 3 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 10 -1.</_>
        <_>
          0 0 8 5 2.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          12 0 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 2 -1.</_>
        <_>
          0 0 12 1 2.</_>
        <_>
          12 1 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 12 12 -1.</_>
        <_>
          0 6 12 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 15 18 -1.</_>
        <_>
          0 6 15 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 1 6 -1.</_>
        <_>
          0 3 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 4 6 -1.</_>
        <_>
          2 1 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 15 1 -1.</_>
        <_>
          5 1 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 10 2 -1.</_>
        <_>
          5 1 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 24 2 -1.</_>
        <_>
          0 2 12 1 2.</_>
        <_>
          12 3 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 24 10 -1.</_>
        <_>
          0 2 12 5 2.</_>
        <_>
          12 7 12 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 7 3 -1.</_>
        <_>
          0 4 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 24 2 -1.</_>
        <_>
          0 3 12 1 2.</_>
        <_>
          12 4 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 6 12 -1.</_>
        <_>
          0 8 6 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 24 6 -1.</_>
        <_>
          0 6 24 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 2 9 -1.</_>
        <_>
          0 8 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 24 2 -1.</_>
        <_>
          0 5 12 1 2.</_>
        <_>
          12 6 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 6 3 -1.</_>
        <_>
          0 7 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 2 -1.</_>
        <_>
          0 6 12 1 2.</_>
        <_>
          12 7 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 22 3 -1.</_>
        <_>
          0 7 22 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 9 -1.</_>
        <_>
          0 9 24 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 16 1 -1.</_>
        <_>
          8 7 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 3 -1.</_>
        <_>
          8 7 8 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 2 -1.</_>
        <_>
          0 7 12 1 2.</_>
        <_>
          12 8 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 4 6 -1.</_>
        <_>
          2 8 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 6 15 -1.</_>
        <_>
          3 8 3 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 4 9 -1.</_>
        <_>
          0 11 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 1 -1.</_>
        <_>
          8 8 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 4 -1.</_>
        <_>
          0 8 12 2 2.</_>
        <_>
          12 10 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 2 3 -1.</_>
        <_>
          0 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 2 9 -1.</_>
        <_>
          0 12 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 5 3 -1.</_>
        <_>
          0 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 5 6 -1.</_>
        <_>
          0 11 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 7 2 -1.</_>
        <_>
          0 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 8 2 -1.</_>
        <_>
          0 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 10 2 -1.</_>
        <_>
          0 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 2 -1.</_>
        <_>
          0 9 11 1 2.</_>
        <_>
          11 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 4 -1.</_>
        <_>
          0 9 12 2 2.</_>
        <_>
          12 11 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 15 -1.</_>
        <_>
          12 9 12 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 15 3 -1.</_>
        <_>
          0 10 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 2 3 -1.</_>
        <_>
          0 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 6 1 -1.</_>
        <_>
          3 10 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 6 14 -1.</_>
        <_>
          3 10 3 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 4 3 -1.</_>
        <_>
          0 11 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 2 -1.</_>
        <_>
          0 10 12 1 2.</_>
        <_>
          12 11 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 4 -1.</_>
        <_>
          0 10 12 2 2.</_>
        <_>
          12 12 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 13 3 -1.</_>
        <_>
          0 11 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 2 3 -1.</_>
        <_>
          0 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 6 8 -1.</_>
        <_>
          0 11 3 4 2.</_>
        <_>
          3 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 10 3 -1.</_>
        <_>
          0 12 10 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 24 2 -1.</_>
        <_>
          0 11 12 1 2.</_>
        <_>
          12 12 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 3 10 -1.</_>
        <_>
          1 12 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 22 10 -1.</_>
        <_>
          11 12 11 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 3 9 -1.</_>
        <_>
          1 13 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 12 10 -1.</_>
        <_>
          6 13 6 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 24 10 -1.</_>
        <_>
          12 13 12 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 24 2 -1.</_>
        <_>
          0 14 12 1 2.</_>
        <_>
          12 15 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 3 8 -1.</_>
        <_>
          1 15 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 12 8 -1.</_>
        <_>
          0 15 6 4 2.</_>
        <_>
          6 19 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 10 6 -1.</_>
        <_>
          0 17 10 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 16 12 8 -1.</_>
        <_>
          0 16 6 4 2.</_>
        <_>
          6 20 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 17 3 7 -1.</_>
        <_>
          1 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 6 3 -1.</_>
        <_>
          0 19 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 6 3 -1.</_>
        <_>
          0 20 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 20 6 3 -1.</_>
        <_>
          0 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 4 3 -1.</_>
        <_>
          0 22 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 5 3 -1.</_>
        <_>
          0 22 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 22 22 2 -1.</_>
        <_>
          11 22 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 6 1 -1.</_>
        <_>
          4 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 15 13 -1.</_>
        <_>
          6 0 5 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 12 6 -1.</_>
        <_>
          1 0 6 3 2.</_>
        <_>
          7 3 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 1 22 2 -1.</_>
        <_>
          1 1 11 1 2.</_>
        <_>
          12 2 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 2 23 9 -1.</_>
        <_>
          1 5 23 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 4 3 -1.</_>
        <_>
          1 4 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 12 18 -1.</_>
        <_>
          5 3 4 18 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 8 3 -1.</_>
        <_>
          1 5 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 23 6 -1.</_>
        <_>
          1 6 23 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 6 4 -1.</_>
        <_>
          1 6 3 2 2.</_>
        <_>
          4 8 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 3 9 -1.</_>
        <_>
          1 9 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 4 3 -1.</_>
        <_>
          1 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 22 2 -1.</_>
        <_>
          1 6 11 1 2.</_>
        <_>
          12 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 12 8 -1.</_>
        <_>
          1 10 12 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 8 4 -1.</_>
        <_>
          1 7 4 2 2.</_>
        <_>
          5 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 20 4 -1.</_>
        <_>
          1 7 10 2 2.</_>
        <_>
          11 9 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 22 6 -1.</_>
        <_>
          1 7 11 3 2.</_>
        <_>
          12 10 11 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 22 14 -1.</_>
        <_>
          12 7 11 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 1 2 -1.</_>
        <_>
          1 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 8 2 -1.</_>
        <_>
          1 8 4 1 2.</_>
        <_>
          5 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 7 4 -1.</_>
        <_>
          1 10 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 22 4 -1.</_>
        <_>
          1 8 11 2 2.</_>
        <_>
          12 10 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 4 3 -1.</_>
        <_>
          3 9 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 4 6 -1.</_>
        <_>
          1 11 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 20 2 -1.</_>
        <_>
          1 9 10 1 2.</_>
        <_>
          11 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 3 13 -1.</_>
        <_>
          2 10 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 4 6 -1.</_>
        <_>
          1 12 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 8 3 -1.</_>
        <_>
          1 11 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 20 2 -1.</_>
        <_>
          1 10 10 1 2.</_>
        <_>
          11 11 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 6 2 -1.</_>
        <_>
          4 11 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 22 2 -1.</_>
        <_>
          1 11 11 1 2.</_>
        <_>
          12 12 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 3 8 -1.</_>
        <_>
          2 12 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 4 1 -1.</_>
        <_>
          3 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 20 2 -1.</_>
        <_>
          1 12 10 1 2.</_>
        <_>
          11 13 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 3 8 -1.</_>
        <_>
          2 13 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 9 3 -1.</_>
        <_>
          1 14 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 21 8 -1.</_>
        <_>
          1 17 21 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 15 8 2 -1.</_>
        <_>
          5 15 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 17 22 2 -1.</_>
        <_>
          1 17 11 1 2.</_>
        <_>
          12 18 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 18 3 6 -1.</_>
        <_>
          2 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 6 1 -1.</_>
        <_>
          5 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 8 6 -1.</_>
        <_>
          2 0 4 3 2.</_>
        <_>
          6 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 12 5 -1.</_>
        <_>
          8 0 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 20 2 -1.</_>
        <_>
          2 3 10 1 2.</_>
        <_>
          12 4 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 3 3 -1.</_>
        <_>
          2 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 20 2 -1.</_>
        <_>
          2 4 10 1 2.</_>
        <_>
          12 5 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 1 3 -1.</_>
        <_>
          2 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 2 3 -1.</_>
        <_>
          2 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 20 2 -1.</_>
        <_>
          2 5 10 1 2.</_>
        <_>
          12 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 22 2 -1.</_>
        <_>
          2 6 11 1 2.</_>
        <_>
          13 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 22 4 -1.</_>
        <_>
          2 6 11 2 2.</_>
        <_>
          13 8 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 15 3 -1.</_>
        <_>
          2 8 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 8 3 -1.</_>
        <_>
          2 9 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 20 4 -1.</_>
        <_>
          2 8 10 2 2.</_>
        <_>
          12 10 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 20 8 -1.</_>
        <_>
          2 9 10 4 2.</_>
        <_>
          12 13 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 22 2 -1.</_>
        <_>
          2 9 11 1 2.</_>
        <_>
          13 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 19 3 -1.</_>
        <_>
          2 10 19 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 4 1 -1.</_>
        <_>
          4 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 22 2 -1.</_>
        <_>
          2 10 11 1 2.</_>
        <_>
          13 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 22 14 -1.</_>
        <_>
          13 10 11 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 20 12 -1.</_>
        <_>
          2 16 20 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 3 5 -1.</_>
        <_>
          3 11 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 20 2 -1.</_>
        <_>
          2 11 10 1 2.</_>
        <_>
          12 12 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 22 2 -1.</_>
        <_>
          2 11 11 1 2.</_>
        <_>
          13 12 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 3 5 -1.</_>
        <_>
          3 12 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 3 9 -1.</_>
        <_>
          3 12 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 3 11 -1.</_>
        <_>
          3 12 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 14 3 3 -1.</_>
        <_>
          3 14 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 14 8 8 -1.</_>
        <_>
          2 14 4 4 2.</_>
        <_>
          6 18 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 3 5 -1.</_>
        <_>
          3 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 3 6 -1.</_>
        <_>
          3 17 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 21 4 -1.</_>
        <_>
          9 17 7 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 18 3 5 -1.</_>
        <_>
          3 18 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 18 10 4 -1.</_>
        <_>
          7 18 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 20 6 2 -1.</_>
        <_>
          5 20 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 21 12 2 -1.</_>
        <_>
          8 21 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 3 5 -1.</_>
        <_>
          4 0 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 9 22 -1.</_>
        <_>
          6 0 3 22 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 12 4 -1.</_>
        <_>
          3 0 6 2 2.</_>
        <_>
          9 2 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 1 3 3 -1.</_>
        <_>
          4 1 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 1 3 20 -1.</_>
        <_>
          4 1 1 20 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 1 6 20 -1.</_>
        <_>
          5 1 2 20 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 2 3 3 -1.</_>
        <_>
          4 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 3 3 -1.</_>
        <_>
          4 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 3 9 -1.</_>
        <_>
          3 6 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 20 19 -1.</_>
        <_>
          13 3 10 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 19 4 -1.</_>
        <_>
          3 5 19 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 1 3 -1.</_>
        <_>
          3 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 6 3 -1.</_>
        <_>
          5 4 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 18 2 -1.</_>
        <_>
          3 4 9 1 2.</_>
        <_>
          12 5 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 16 6 -1.</_>
        <_>
          3 6 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 3 1 -1.</_>
        <_>
          4 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 3 2 -1.</_>
        <_>
          4 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 2 3 -1.</_>
        <_>
          3 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 10 3 -1.</_>
        <_>
          8 5 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 18 3 -1.</_>
        <_>
          9 5 6 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 18 2 -1.</_>
        <_>
          3 5 9 1 2.</_>
        <_>
          12 6 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 2 1 -1.</_>
        <_>
          4 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 1 3 -1.</_>
        <_>
          3 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 3 3 -1.</_>
        <_>
          3 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 6 6 -1.</_>
        <_>
          3 8 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 18 2 -1.</_>
        <_>
          3 6 9 1 2.</_>
        <_>
          12 7 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 17 6 -1.</_>
        <_>
          3 8 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 3 1 -1.</_>
        <_>
          4 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 4 2 -1.</_>
        <_>
          3 8 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 6 6 -1.</_>
        <_>
          3 9 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 18 4 -1.</_>
        <_>
          3 7 9 2 2.</_>
        <_>
          12 9 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 20 11 -1.</_>
        <_>
          13 7 10 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 17 6 -1.</_>
        <_>
          3 9 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 1 -1.</_>
        <_>
          4 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 2 -1.</_>
        <_>
          4 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 9 2 -1.</_>
        <_>
          6 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 18 2 -1.</_>
        <_>
          3 9 9 1 2.</_>
        <_>
          12 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 1 -1.</_>
        <_>
          4 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 13 -1.</_>
        <_>
          4 10 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 6 2 -1.</_>
        <_>
          3 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 2 -1.</_>
        <_>
          4 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 3 -1.</_>
        <_>
          4 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 5 -1.</_>
        <_>
          4 11 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 13 -1.</_>
        <_>
          4 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 6 2 -1.</_>
        <_>
          3 11 3 1 2.</_>
        <_>
          6 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 4 -1.</_>
        <_>
          3 13 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 4 8 -1.</_>
        <_>
          3 15 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 3 -1.</_>
        <_>
          4 12 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 6 -1.</_>
        <_>
          3 15 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 9 7 -1.</_>
        <_>
          6 12 3 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 4 8 -1.</_>
        <_>
          3 16 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 8 8 -1.</_>
        <_>
          3 16 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 19 6 -1.</_>
        <_>
          3 15 19 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 13 18 2 -1.</_>
        <_>
          3 13 9 1 2.</_>
        <_>
          12 14 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 15 4 2 -1.</_>
        <_>
          5 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 15 4 3 -1.</_>
        <_>
          5 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 15 6 2 -1.</_>
        <_>
          6 15 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 8 8 -1.</_>
        <_>
          3 16 4 4 2.</_>
        <_>
          7 20 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 20 3 4 -1.</_>
        <_>
          4 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 1 3 8 -1.</_>
        <_>
          5 1 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 1 3 12 -1.</_>
        <_>
          4 5 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 1 15 10 -1.</_>
        <_>
          4 6 15 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 3 3 -1.</_>
        <_>
          5 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 6 5 -1.</_>
        <_>
          6 2 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 16 2 -1.</_>
        <_>
          4 2 8 1 2.</_>
        <_>
          12 3 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 3 2 -1.</_>
        <_>
          5 3 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 6 1 -1.</_>
        <_>
          6 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 6 5 -1.</_>
        <_>
          6 3 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 9 3 -1.</_>
        <_>
          7 3 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 2 -1.</_>
        <_>
          4 3 8 1 2.</_>
        <_>
          12 4 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 8 -1.</_>
        <_>
          4 3 8 4 2.</_>
        <_>
          12 7 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 17 8 -1.</_>
        <_>
          4 7 17 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 1 4 -1.</_>
        <_>
          4 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 16 2 -1.</_>
        <_>
          4 4 8 1 2.</_>
        <_>
          12 5 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 16 10 -1.</_>
        <_>
          4 4 8 5 2.</_>
        <_>
          12 9 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 20 6 -1.</_>
        <_>
          4 6 20 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 16 2 -1.</_>
        <_>
          4 5 8 1 2.</_>
        <_>
          12 6 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 16 9 -1.</_>
        <_>
          4 8 16 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 2 -1.</_>
        <_>
          4 6 1 1 2.</_>
        <_>
          5 7 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 2 -1.</_>
        <_>
          4 7 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 6 1 -1.</_>
        <_>
          6 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 3 -1.</_>
        <_>
          4 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 3 3 -1.</_>
        <_>
          4 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 2 -1.</_>
        <_>
          4 6 8 1 2.</_>
        <_>
          12 7 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 15 6 -1.</_>
        <_>
          4 8 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 2 3 -1.</_>
        <_>
          4 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 4 3 -1.</_>
        <_>
          6 7 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 4 3 -1.</_>
        <_>
          4 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 5 3 -1.</_>
        <_>
          4 8 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 5 6 -1.</_>
        <_>
          4 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 7 3 -1.</_>
        <_>
          4 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 18 2 -1.</_>
        <_>
          4 7 9 1 2.</_>
        <_>
          13 8 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 18 4 -1.</_>
        <_>
          4 7 9 2 2.</_>
        <_>
          13 9 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 3 -1.</_>
        <_>
          4 8 16 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 6 -1.</_>
        <_>
          4 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 2 -1.</_>
        <_>
          4 8 17 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 3 -1.</_>
        <_>
          4 8 17 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 6 -1.</_>
        <_>
          4 9 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 2 2 -1.</_>
        <_>
          4 8 1 1 2.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 16 2 -1.</_>
        <_>
          4 8 8 1 2.</_>
        <_>
          12 9 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 18 4 -1.</_>
        <_>
          4 8 9 2 2.</_>
        <_>
          13 10 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 1 -1.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 1 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          4 9 1 1 2.</_>
        <_>
          5 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          5 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 6 1 -1.</_>
        <_>
          6 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 9 -1.</_>
        <_>
          4 12 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 1 -1.</_>
        <_>
          9 9 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 5 3 -1.</_>
        <_>
          4 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 2 -1.</_>
        <_>
          9 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 3 -1.</_>
        <_>
          9 9 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 2 -1.</_>
        <_>
          4 9 8 1 2.</_>
        <_>
          12 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 6 -1.</_>
        <_>
          4 9 8 3 2.</_>
        <_>
          12 12 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 18 2 -1.</_>
        <_>
          4 9 9 1 2.</_>
        <_>
          13 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 20 2 -1.</_>
        <_>
          4 9 10 1 2.</_>
        <_>
          14 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 17 9 -1.</_>
        <_>
          4 12 17 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 18 3 -1.</_>
        <_>
          4 10 18 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 1 -1.</_>
        <_>
          5 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 1 -1.</_>
        <_>
          5 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 2 -1.</_>
        <_>
          4 10 1 1 2.</_>
        <_>
          5 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 6 3 -1.</_>
        <_>
          7 10 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 18 2 -1.</_>
        <_>
          4 10 9 1 2.</_>
        <_>
          13 11 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 17 6 -1.</_>
        <_>
          4 12 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 3 -1.</_>
        <_>
          5 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 1 8 -1.</_>
        <_>
          4 15 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 6 -1.</_>
        <_>
          5 11 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 6 3 -1.</_>
        <_>
          6 11 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 15 2 -1.</_>
        <_>
          4 12 15 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 1 -1.</_>
        <_>
          5 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 4 4 -1.</_>
        <_>
          6 12 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 8 -1.</_>
        <_>
          4 16 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 17 12 -1.</_>
        <_>
          4 16 17 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 3 1 -1.</_>
        <_>
          5 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 3 9 -1.</_>
        <_>
          4 16 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 14 4 2 -1.</_>
        <_>
          6 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 4 2 -1.</_>
        <_>
          6 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 9 4 -1.</_>
        <_>
          7 15 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 16 4 -1.</_>
        <_>
          4 15 8 2 2.</_>
        <_>
          12 17 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 18 3 5 -1.</_>
        <_>
          5 18 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 18 3 6 -1.</_>
        <_>
          5 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 18 15 5 -1.</_>
        <_>
          9 18 5 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 18 9 6 -1.</_>
        <_>
          4 21 9 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 1 14 2 -1.</_>
        <_>
          5 1 7 1 2.</_>
        <_>
          12 2 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 1 11 8 -1.</_>
        <_>
          5 5 11 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 3 3 -1.</_>
        <_>
          6 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 6 2 -1.</_>
        <_>
          7 2 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 14 2 -1.</_>
        <_>
          5 2 7 1 2.</_>
        <_>
          12 3 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 14 8 -1.</_>
        <_>
          5 6 14 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 16 10 -1.</_>
        <_>
          5 7 16 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 6 1 -1.</_>
        <_>
          7 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 6 2 -1.</_>
        <_>
          7 3 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 4 10 -1.</_>
        <_>
          5 3 2 5 2.</_>
        <_>
          7 8 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 9 12 -1.</_>
        <_>
          8 3 3 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 14 2 -1.</_>
        <_>
          5 3 7 1 2.</_>
        <_>
          12 4 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 15 8 -1.</_>
        <_>
          5 7 15 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 2 4 -1.</_>
        <_>
          5 4 1 2 2.</_>
        <_>
          6 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 6 4 -1.</_>
        <_>
          7 4 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 4 12 -1.</_>
        <_>
          7 4 2 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 12 8 -1.</_>
        <_>
          9 4 4 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 14 2 -1.</_>
        <_>
          5 4 7 1 2.</_>
        <_>
          12 5 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 2 2 -1.</_>
        <_>
          5 5 1 1 2.</_>
        <_>
          6 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 2 4 -1.</_>
        <_>
          5 5 1 2 2.</_>
        <_>
          6 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 6 6 -1.</_>
        <_>
          5 7 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 2 -1.</_>
        <_>
          5 5 7 1 2.</_>
        <_>
          12 6 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 16 2 -1.</_>
        <_>
          5 5 8 1 2.</_>
        <_>
          13 6 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 13 6 -1.</_>
        <_>
          5 7 13 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 6 -1.</_>
        <_>
          5 7 14 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 15 6 -1.</_>
        <_>
          5 7 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 15 9 -1.</_>
        <_>
          5 8 15 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 1 2 -1.</_>
        <_>
          5 7 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 2 4 -1.</_>
        <_>
          5 6 1 2 2.</_>
        <_>
          6 8 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 6 1 -1.</_>
        <_>
          7 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 4 3 -1.</_>
        <_>
          5 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 14 2 -1.</_>
        <_>
          5 6 7 1 2.</_>
        <_>
          12 7 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 2 2 -1.</_>
        <_>
          6 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 2 6 -1.</_>
        <_>
          5 7 1 3 2.</_>
        <_>
          6 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 1 -1.</_>
        <_>
          7 7 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 6 5 -1.</_>
        <_>
          7 7 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 2 -1.</_>
        <_>
          5 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 3 -1.</_>
        <_>
          5 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 6 -1.</_>
        <_>
          5 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 3 -1.</_>
        <_>
          5 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 6 -1.</_>
        <_>
          5 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 5 6 -1.</_>
        <_>
          5 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 4 -1.</_>
        <_>
          5 7 7 2 2.</_>
        <_>
          12 9 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 2 -1.</_>
        <_>
          5 8 14 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 4 -1.</_>
        <_>
          5 9 14 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 15 2 -1.</_>
        <_>
          5 8 15 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 15 6 -1.</_>
        <_>
          5 9 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 1 3 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 2 2 -1.</_>
        <_>
          5 8 1 1 2.</_>
        <_>
          6 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 5 -1.</_>
        <_>
          7 8 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 12 4 -1.</_>
        <_>
          9 8 4 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 15 3 -1.</_>
        <_>
          10 8 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 14 4 -1.</_>
        <_>
          5 8 7 2 2.</_>
        <_>
          12 10 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 4 -1.</_>
        <_>
          7 9 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 3 -1.</_>
        <_>
          5 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 8 8 -1.</_>
        <_>
          5 9 4 4 2.</_>
        <_>
          9 13 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 15 2 -1.</_>
        <_>
          10 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 2 -1.</_>
        <_>
          5 9 7 1 2.</_>
        <_>
          12 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 12 -1.</_>
        <_>
          5 9 7 6 2.</_>
        <_>
          12 15 7 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 18 2 -1.</_>
        <_>
          5 9 9 1 2.</_>
        <_>
          14 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 13 3 -1.</_>
        <_>
          5 10 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 15 6 -1.</_>
        <_>
          5 12 15 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 2 2 -1.</_>
        <_>
          5 10 1 1 2.</_>
        <_>
          6 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 3 3 -1.</_>
        <_>
          6 10 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 2 -1.</_>
        <_>
          6 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 3 -1.</_>
        <_>
          6 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 13 -1.</_>
        <_>
          6 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 14 2 -1.</_>
        <_>
          5 12 14 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 12 1 6 -1.</_>
        <_>
          5 15 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 13 15 8 -1.</_>
        <_>
          5 17 15 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 14 3 3 -1.</_>
        <_>
          5 15 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 15 2 2 -1.</_>
        <_>
          6 15 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 19 3 5 -1.</_>
        <_>
          6 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 21 3 3 -1.</_>
        <_>
          6 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 1 6 -1.</_>
        <_>
          6 3 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 11 10 -1.</_>
        <_>
          6 5 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 6 12 -1.</_>
        <_>
          8 1 2 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 3 6 -1.</_>
        <_>
          7 2 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 6 2 -1.</_>
        <_>
          8 2 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 6 10 -1.</_>
        <_>
          8 2 2 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 12 4 -1.</_>
        <_>
          6 4 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 6 4 -1.</_>
        <_>
          8 3 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 9 1 -1.</_>
        <_>
          9 3 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 3 3 -1.</_>
        <_>
          7 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 6 4 -1.</_>
        <_>
          8 4 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 3 2 -1.</_>
        <_>
          7 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 3 3 -1.</_>
        <_>
          7 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 2 9 -1.</_>
        <_>
          6 8 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 12 2 -1.</_>
        <_>
          6 5 6 1 2.</_>
        <_>
          12 6 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 4 1 -1.</_>
        <_>
          8 6 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 12 2 -1.</_>
        <_>
          6 6 6 1 2.</_>
        <_>
          12 7 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 1 6 -1.</_>
        <_>
          6 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 2 -1.</_>
        <_>
          6 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 3 -1.</_>
        <_>
          6 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 6 -1.</_>
        <_>
          6 9 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 3 6 -1.</_>
        <_>
          6 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 12 2 -1.</_>
        <_>
          6 7 6 1 2.</_>
        <_>
          12 8 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 8 12 -1.</_>
        <_>
          6 13 8 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 12 15 -1.</_>
        <_>
          6 12 12 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 6 -1.</_>
        <_>
          6 11 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 3 2 -1.</_>
        <_>
          6 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 3 -1.</_>
        <_>
          10 8 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 2 -1.</_>
        <_>
          6 8 6 1 2.</_>
        <_>
          12 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 2 -1.</_>
        <_>
          6 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 3 -1.</_>
        <_>
          6 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 6 1 -1.</_>
        <_>
          9 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 3 3 -1.</_>
        <_>
          6 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          6 9 6 1 2.</_>
        <_>
          12 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 13 12 -1.</_>
        <_>
          6 13 13 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 1 3 -1.</_>
        <_>
          6 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 2 -1.</_>
        <_>
          7 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          7 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 3 14 -1.</_>
        <_>
          7 10 1 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          6 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 6 3 -1.</_>
        <_>
          8 10 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 3 3 -1.</_>
        <_>
          6 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 9 5 -1.</_>
        <_>
          9 10 3 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 1 -1.</_>
        <_>
          10 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 8 4 -1.</_>
        <_>
          6 10 4 2 2.</_>
        <_>
          10 12 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 2 -1.</_>
        <_>
          6 10 6 1 2.</_>
        <_>
          12 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 12 -1.</_>
        <_>
          6 10 6 6 2.</_>
        <_>
          12 16 6 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 18 1 -1.</_>
        <_>
          15 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 13 3 -1.</_>
        <_>
          6 11 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 2 2 -1.</_>
        <_>
          6 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 2 3 -1.</_>
        <_>
          6 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 2 -1.</_>
        <_>
          6 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 12 3 -1.</_>
        <_>
          6 12 12 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 13 3 -1.</_>
        <_>
          6 12 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 12 14 2 -1.</_>
        <_>
          6 12 7 1 2.</_>
        <_>
          13 13 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 12 13 2 -1.</_>
        <_>
          6 13 13 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 14 1 3 -1.</_>
        <_>
          6 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 14 2 2 -1.</_>
        <_>
          7 14 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 15 2 3 -1.</_>
        <_>
          6 16 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 17 10 6 -1.</_>
        <_>
          6 20 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 18 3 6 -1.</_>
        <_>
          7 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 19 3 5 -1.</_>
        <_>
          7 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 20 9 4 -1.</_>
        <_>
          6 22 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 23 3 1 -1.</_>
        <_>
          7 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 2 8 -1.</_>
        <_>
          7 0 1 4 2.</_>
        <_>
          8 4 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 10 1 -1.</_>
        <_>
          12 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 2 4 -1.</_>
        <_>
          7 3 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 10 1 -1.</_>
        <_>
          12 1 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 4 21 -1.</_>
        <_>
          9 2 2 21 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 3 1 3 -1.</_>
        <_>
          7 4 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 3 3 5 -1.</_>
        <_>
          8 3 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 3 10 -1.</_>
        <_>
          8 4 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 2 2 -1.</_>
        <_>
          8 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 2 -1.</_>
        <_>
          8 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 3 -1.</_>
        <_>
          8 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          8 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 2 7 -1.</_>
        <_>
          8 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 2 6 -1.</_>
        <_>
          7 7 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 11 6 -1.</_>
        <_>
          7 7 11 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 3 1 -1.</_>
        <_>
          8 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 1 3 -1.</_>
        <_>
          7 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 4 6 -1.</_>
        <_>
          9 6 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 10 2 -1.</_>
        <_>
          7 6 5 1 2.</_>
        <_>
          12 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 12 2 -1.</_>
        <_>
          7 6 6 1 2.</_>
        <_>
          13 7 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 2 -1.</_>
        <_>
          7 8 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 3 -1.</_>
        <_>
          7 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 6 -1.</_>
        <_>
          7 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 2 4 -1.</_>
        <_>
          7 9 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 10 2 -1.</_>
        <_>
          7 7 5 1 2.</_>
        <_>
          12 8 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 1 3 -1.</_>
        <_>
          7 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 4 -1.</_>
        <_>
          7 8 1 2 2.</_>
        <_>
          8 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 10 2 -1.</_>
        <_>
          7 8 5 1 2.</_>
        <_>
          12 9 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 1 2 -1.</_>
        <_>
          7 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 1 3 -1.</_>
        <_>
          7 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 3 3 -1.</_>
        <_>
          8 9 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 4 6 -1.</_>
        <_>
          7 9 2 3 2.</_>
        <_>
          9 12 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 6 10 -1.</_>
        <_>
          7 9 3 5 2.</_>
        <_>
          10 14 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 12 2 -1.</_>
        <_>
          11 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 10 2 -1.</_>
        <_>
          7 9 5 1 2.</_>
        <_>
          12 10 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 12 2 -1.</_>
        <_>
          7 9 6 1 2.</_>
        <_>
          13 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 3 1 -1.</_>
        <_>
          8 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 1 3 -1.</_>
        <_>
          7 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 2 3 -1.</_>
        <_>
          7 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 6 4 -1.</_>
        <_>
          9 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 10 2 -1.</_>
        <_>
          7 10 5 1 2.</_>
        <_>
          12 11 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 2 1 -1.</_>
        <_>
          8 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 2 2 -1.</_>
        <_>
          7 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 6 4 -1.</_>
        <_>
          9 11 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 14 1 3 -1.</_>
        <_>
          7 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 16 10 8 -1.</_>
        <_>
          7 20 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 3 6 -1.</_>
        <_>
          8 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 9 6 -1.</_>
        <_>
          7 20 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 19 3 3 -1.</_>
        <_>
          8 19 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 3 4 -1.</_>
        <_>
          8 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 7 4 -1.</_>
        <_>
          7 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 11 4 -1.</_>
        <_>
          7 22 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 22 3 2 -1.</_>
        <_>
          8 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 2 -1.</_>
        <_>
          12 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 2 -1.</_>
        <_>
          8 1 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 10 -1.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 10 -1.</_>
        <_>
          8 0 8 5 2.</_>
        <_>
          16 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 10 3 -1.</_>
        <_>
          8 1 10 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 8 1 -1.</_>
        <_>
          12 1 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 2 3 2 -1.</_>
        <_>
          9 2 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 2 8 20 -1.</_>
        <_>
          12 2 4 20 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 3 8 -1.</_>
        <_>
          9 3 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 3 9 -1.</_>
        <_>
          9 3 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 6 1 -1.</_>
        <_>
          11 3 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 4 3 -1.</_>
        <_>
          8 4 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 8 2 -1.</_>
        <_>
          8 3 4 1 2.</_>
        <_>
          12 4 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 2 -1.</_>
        <_>
          9 4 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 2 8 -1.</_>
        <_>
          9 4 1 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 3 -1.</_>
        <_>
          8 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 8 2 -1.</_>
        <_>
          8 4 4 1 2.</_>
        <_>
          12 5 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 7 15 -1.</_>
        <_>
          8 9 7 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 2 -1.</_>
        <_>
          9 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 3 -1.</_>
        <_>
          9 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 5 -1.</_>
        <_>
          9 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 6 -1.</_>
        <_>
          9 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 7 -1.</_>
        <_>
          9 5 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 4 3 -1.</_>
        <_>
          8 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 8 12 -1.</_>
        <_>
          12 5 4 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 8 19 -1.</_>
        <_>
          12 5 4 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 3 5 -1.</_>
        <_>
          9 6 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 10 2 -1.</_>
        <_>
          8 6 5 1 2.</_>
        <_>
          13 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 8 2 2 -1.</_>
        <_>
          8 8 1 1 2.</_>
        <_>
          9 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 8 1 6 -1.</_>
        <_>
          8 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 8 3 3 -1.</_>
        <_>
          8 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 1 3 -1.</_>
        <_>
          8 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 3 2 -1.</_>
        <_>
          9 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 2 6 -1.</_>
        <_>
          8 9 1 3 2.</_>
        <_>
          9 12 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 1 -1.</_>
        <_>
          9 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 1 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 2 -1.</_>
        <_>
          8 10 1 1 2.</_>
        <_>
          9 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 2 -1.</_>
        <_>
          9 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 2 -1.</_>
        <_>
          9 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 4 8 -1.</_>
        <_>
          8 10 2 4 2.</_>
        <_>
          10 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 8 2 -1.</_>
        <_>
          8 10 4 1 2.</_>
        <_>
          12 11 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 2 2 -1.</_>
        <_>
          8 11 1 1 2.</_>
        <_>
          9 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 8 -1.</_>
        <_>
          8 11 2 4 2.</_>
        <_>
          10 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 10 -1.</_>
        <_>
          8 11 2 5 2.</_>
        <_>
          10 16 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 13 9 10 -1.</_>
        <_>
          8 18 9 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 15 4 4 -1.</_>
        <_>
          10 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 9 3 -1.</_>
        <_>
          11 16 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 3 5 -1.</_>
        <_>
          9 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 3 3 -1.</_>
        <_>
          9 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 1 2 -1.</_>
        <_>
          9 1 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 2 4 -1.</_>
        <_>
          10 0 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 1 -1.</_>
        <_>
          12 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 5 4 -1.</_>
        <_>
          9 2 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 10 -1.</_>
        <_>
          9 5 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 14 8 -1.</_>
        <_>
          9 0 7 4 2.</_>
        <_>
          16 4 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 7 10 -1.</_>
        <_>
          9 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 14 10 -1.</_>
        <_>
          9 0 7 5 2.</_>
        <_>
          16 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 14 12 -1.</_>
        <_>
          9 0 7 6 2.</_>
        <_>
          16 6 7 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 3 12 -1.</_>
        <_>
          10 1 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 4 15 -1.</_>
        <_>
          11 1 2 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 6 1 -1.</_>
        <_>
          12 1 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 2 2 -1.</_>
        <_>
          10 2 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 18 -1.</_>
        <_>
          12 2 3 18 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 15 3 -1.</_>
        <_>
          9 3 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 3 9 -1.</_>
        <_>
          10 3 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 8 6 -1.</_>
        <_>
          9 6 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 15 15 -1.</_>
        <_>
          9 8 15 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 3 4 -1.</_>
        <_>
          10 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 6 2 -1.</_>
        <_>
          9 4 3 1 2.</_>
        <_>
          12 5 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 14 5 -1.</_>
        <_>
          16 4 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 2 5 -1.</_>
        <_>
          10 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 6 -1.</_>
        <_>
          10 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 4 15 -1.</_>
        <_>
          11 5 2 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 3 -1.</_>
        <_>
          9 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 4 3 -1.</_>
        <_>
          9 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 4 -1.</_>
        <_>
          11 6 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 3 3 -1.</_>
        <_>
          9 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 6 7 -1.</_>
        <_>
          12 6 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 3 -1.</_>
        <_>
          9 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 15 10 -1.</_>
        <_>
          9 11 15 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 7 6 2 -1.</_>
        <_>
          9 7 3 1 2.</_>
        <_>
          12 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 3 3 -1.</_>
        <_>
          9 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 7 10 -1.</_>
        <_>
          9 13 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 2 2 -1.</_>
        <_>
          10 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 3 3 -1.</_>
        <_>
          9 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 9 6 -1.</_>
        <_>
          12 9 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 2 4 -1.</_>
        <_>
          9 10 1 2 2.</_>
        <_>
          10 12 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 6 2 -1.</_>
        <_>
          9 10 3 1 2.</_>
        <_>
          12 11 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 8 1 -1.</_>
        <_>
          13 10 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 15 3 -1.</_>
        <_>
          9 11 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 11 2 4 -1.</_>
        <_>
          9 11 1 2 2.</_>
        <_>
          10 13 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 11 2 6 -1.</_>
        <_>
          9 11 1 3 2.</_>
        <_>
          10 14 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 13 2 11 -1.</_>
        <_>
          10 13 1 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 14 6 3 -1.</_>
        <_>
          11 14 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 4 3 -1.</_>
        <_>
          11 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 4 -1.</_>
        <_>
          11 16 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 8 -1.</_>
        <_>
          11 16 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 3 -1.</_>
        <_>
          9 17 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 6 2 -1.</_>
        <_>
          11 17 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 6 7 -1.</_>
        <_>
          11 17 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 18 5 3 -1.</_>
        <_>
          9 19 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 3 5 -1.</_>
        <_>
          10 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 7 3 -1.</_>
        <_>
          9 20 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 3 4 -1.</_>
        <_>
          10 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 3 2 -1.</_>
        <_>
          9 21 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 5 3 -1.</_>
        <_>
          9 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 6 3 -1.</_>
        <_>
          9 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 6 4 -1.</_>
        <_>
          9 22 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 7 3 -1.</_>
        <_>
          9 21 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 8 4 -1.</_>
        <_>
          9 22 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 3 2 -1.</_>
        <_>
          10 21 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 22 3 2 -1.</_>
        <_>
          10 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 1 6 -1.</_>
        <_>
          10 3 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 6 1 -1.</_>
        <_>
          13 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 8 -1.</_>
        <_>
          10 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 14 10 -1.</_>
        <_>
          10 0 7 5 2.</_>
        <_>
          17 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 4 2 -1.</_>
        <_>
          10 1 2 1 2.</_>
        <_>
          12 2 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 2 1 6 -1.</_>
        <_>
          10 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 6 1 -1.</_>
        <_>
          13 3 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 9 1 -1.</_>
        <_>
          13 3 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 3 3 -1.</_>
        <_>
          10 4 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 3 -1.</_>
        <_>
          11 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 2 8 -1.</_>
        <_>
          11 4 1 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 3 3 -1.</_>
        <_>
          11 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 4 11 -1.</_>
        <_>
          12 5 2 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 6 3 -1.</_>
        <_>
          10 7 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 2 3 -1.</_>
        <_>
          10 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 4 7 -1.</_>
        <_>
          12 7 2 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 9 6 -1.</_>
        <_>
          13 7 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 4 3 -1.</_>
        <_>
          10 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 8 2 3 -1.</_>
        <_>
          10 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 8 4 2 -1.</_>
        <_>
          10 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 8 8 10 -1.</_>
        <_>
          10 13 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 1 3 -1.</_>
        <_>
          10 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 3 -1.</_>
        <_>
          10 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 6 4 -1.</_>
        <_>
          13 9 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 14 3 -1.</_>
        <_>
          10 11 14 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 11 1 3 -1.</_>
        <_>
          10 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 11 4 3 -1.</_>
        <_>
          10 12 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 12 1 3 -1.</_>
        <_>
          10 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 12 2 8 -1.</_>
        <_>
          10 12 1 4 2.</_>
        <_>
          11 16 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 15 4 3 -1.</_>
        <_>
          10 16 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 15 6 6 -1.</_>
        <_>
          10 17 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 16 6 8 -1.</_>
        <_>
          10 16 3 4 2.</_>
        <_>
          13 20 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 16 4 2 -1.</_>
        <_>
          10 17 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 16 4 3 -1.</_>
        <_>
          10 17 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          10 18 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 5 3 -1.</_>
        <_>
          10 18 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 18 5 3 -1.</_>
        <_>
          10 19 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 19 5 3 -1.</_>
        <_>
          10 20 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 3 -1.</_>
        <_>
          11 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 4 -1.</_>
        <_>
          11 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 4 3 -1.</_>
        <_>
          10 21 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 5 3 -1.</_>
        <_>
          10 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 3 1 -1.</_>
        <_>
          11 21 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 3 3 -1.</_>
        <_>
          11 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 6 3 -1.</_>
        <_>
          12 21 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 5 2 -1.</_>
        <_>
          10 22 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 1 -1.</_>
        <_>
          11 22 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 2 -1.</_>
        <_>
          11 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 0 2 12 -1.</_>
        <_>
          11 4 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 0 12 19 -1.</_>
        <_>
          15 0 4 19 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 4 20 -1.</_>
        <_>
          13 2 2 20 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 3 -1.</_>
        <_>
          12 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 5 -1.</_>
        <_>
          12 3 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 6 -1.</_>
        <_>
          12 3 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 7 -1.</_>
        <_>
          12 3 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 2 3 -1.</_>
        <_>
          11 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 12 14 -1.</_>
        <_>
          15 3 4 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 5 -1.</_>
        <_>
          12 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 2 3 -1.</_>
        <_>
          11 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 9 1 -1.</_>
        <_>
          14 4 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 3 -1.</_>
        <_>
          11 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 8 4 -1.</_>
        <_>
          11 7 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 2 3 -1.</_>
        <_>
          11 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 3 -1.</_>
        <_>
          11 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 1 3 -1.</_>
        <_>
          11 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 2 -1.</_>
        <_>
          11 7 1 1 2.</_>
        <_>
          12 8 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 3 -1.</_>
        <_>
          11 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 4 2 -1.</_>
        <_>
          11 7 2 1 2.</_>
        <_>
          13 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 4 3 -1.</_>
        <_>
          11 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 1 3 -1.</_>
        <_>
          11 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 1 10 -1.</_>
        <_>
          11 13 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 2 3 -1.</_>
        <_>
          11 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 3 3 -1.</_>
        <_>
          11 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 8 8 -1.</_>
        <_>
          11 8 4 4 2.</_>
        <_>
          15 12 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 7 10 -1.</_>
        <_>
          11 13 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 6 6 -1.</_>
        <_>
          13 9 2 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 4 3 -1.</_>
        <_>
          11 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 4 -1.</_>
        <_>
          13 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 8 -1.</_>
        <_>
          11 10 3 4 2.</_>
        <_>
          14 14 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 4 3 -1.</_>
        <_>
          11 11 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 5 3 -1.</_>
        <_>
          11 11 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 11 1 3 -1.</_>
        <_>
          11 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 11 10 10 -1.</_>
        <_>
          11 11 5 5 2.</_>
        <_>
          16 16 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 13 6 2 -1.</_>
        <_>
          13 13 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 14 2 9 -1.</_>
        <_>
          11 17 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 15 1 2 -1.</_>
        <_>
          11 16 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 4 -1.</_>
        <_>
          12 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 3 -1.</_>
        <_>
          11 21 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 2 1 -1.</_>
        <_>
          12 21 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 2 -1.</_>
        <_>
          12 21 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 2 3 -1.</_>
        <_>
          12 21 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 2 -1.</_>
        <_>
          11 22 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 23 3 1 -1.</_>
        <_>
          12 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 8 12 -1.</_>
        <_>
          12 0 4 6 2.</_>
        <_>
          16 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 6 -1.</_>
        <_>
          12 0 6 3 2.</_>
        <_>
          18 3 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 1 3 -1.</_>
        <_>
          12 2 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 2 7 -1.</_>
        <_>
          13 1 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 12 4 -1.</_>
        <_>
          12 1 6 2 2.</_>
        <_>
          18 3 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 3 3 -1.</_>
        <_>
          13 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 3 7 -1.</_>
        <_>
          13 2 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 6 1 -1.</_>
        <_>
          14 2 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 6 4 -1.</_>
        <_>
          14 2 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 6 18 -1.</_>
        <_>
          12 8 6 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 6 11 -1.</_>
        <_>
          14 3 2 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 9 3 -1.</_>
        <_>
          15 3 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 12 3 -1.</_>
        <_>
          12 4 12 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 2 12 -1.</_>
        <_>
          13 4 1 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 2 3 -1.</_>
        <_>
          12 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 5 -1.</_>
        <_>
          13 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 4 3 -1.</_>
        <_>
          12 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 1 3 -1.</_>
        <_>
          12 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 2 3 -1.</_>
        <_>
          12 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 8 4 -1.</_>
        <_>
          12 6 4 2 2.</_>
        <_>
          16 8 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 1 3 -1.</_>
        <_>
          12 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 2 3 -1.</_>
        <_>
          12 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 1 3 -1.</_>
        <_>
          12 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 3 3 -1.</_>
        <_>
          12 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 4 3 -1.</_>
        <_>
          12 9 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 1 3 -1.</_>
        <_>
          12 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 2 12 -1.</_>
        <_>
          12 10 1 6 2.</_>
        <_>
          13 16 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 4 10 -1.</_>
        <_>
          12 10 2 5 2.</_>
        <_>
          14 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 2 3 -1.</_>
        <_>
          12 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 4 4 -1.</_>
        <_>
          14 11 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 4 8 -1.</_>
        <_>
          12 11 2 4 2.</_>
        <_>
          14 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 15 6 5 -1.</_>
        <_>
          14 15 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 15 10 4 -1.</_>
        <_>
          12 15 5 2 2.</_>
        <_>
          17 17 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 16 4 3 -1.</_>
        <_>
          14 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 17 3 3 -1.</_>
        <_>
          13 17 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 17 8 6 -1.</_>
        <_>
          12 17 4 3 2.</_>
        <_>
          16 20 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 18 12 6 -1.</_>
        <_>
          12 18 6 3 2.</_>
        <_>
          18 21 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 21 3 3 -1.</_>
        <_>
          13 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 11 14 -1.</_>
        <_>
          13 7 11 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 3 -1.</_>
        <_>
          14 2 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 1 4 -1.</_>
        <_>
          13 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 3 3 -1.</_>
        <_>
          14 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 6 1 -1.</_>
        <_>
          15 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 1 2 -1.</_>
        <_>
          13 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 3 7 -1.</_>
        <_>
          14 4 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 3 8 -1.</_>
        <_>
          14 4 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 6 -1.</_>
        <_>
          14 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 6 1 3 -1.</_>
        <_>
          13 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 6 6 -1.</_>
        <_>
          15 7 2 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 3 3 -1.</_>
        <_>
          13 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 6 8 -1.</_>
        <_>
          15 8 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 3 4 -1.</_>
        <_>
          14 9 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 4 3 -1.</_>
        <_>
          15 9 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 6 4 -1.</_>
        <_>
          15 9 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 9 2 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 9 2 -1.</_>
        <_>
          13 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 2 -1.</_>
        <_>
          14 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 4 1 -1.</_>
        <_>
          15 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 4 4 -1.</_>
        <_>
          13 10 2 2 2.</_>
        <_>
          15 12 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 2 3 -1.</_>
        <_>
          13 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 3 3 -1.</_>
        <_>
          13 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 12 3 3 -1.</_>
        <_>
          13 13 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 13 2 6 -1.</_>
        <_>
          13 13 1 3 2.</_>
        <_>
          14 16 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 15 2 5 -1.</_>
        <_>
          14 15 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 19 3 3 -1.</_>
        <_>
          14 19 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 20 3 3 -1.</_>
        <_>
          14 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 22 3 2 -1.</_>
        <_>
          14 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 1 10 -1.</_>
        <_>
          14 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 2 7 -1.</_>
        <_>
          15 0 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 2 22 -1.</_>
        <_>
          14 0 1 11 2.</_>
        <_>
          15 11 1 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 6 -1.</_>
        <_>
          14 0 5 3 2.</_>
        <_>
          19 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 8 -1.</_>
        <_>
          14 0 5 4 2.</_>
        <_>
          19 4 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 12 -1.</_>
        <_>
          14 0 5 6 2.</_>
        <_>
          19 6 5 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 1 2 2 -1.</_>
        <_>
          15 1 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 1 4 4 -1.</_>
        <_>
          14 3 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 1 10 2 -1.</_>
        <_>
          19 1 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 6 7 -1.</_>
        <_>
          16 2 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 3 2 4 -1.</_>
        <_>
          14 3 1 2 2.</_>
        <_>
          15 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 3 -1.</_>
        <_>
          15 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 6 1 -1.</_>
        <_>
          16 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 3 -1.</_>
        <_>
          14 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 2 -1.</_>
        <_>
          15 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 3 -1.</_>
        <_>
          15 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 4 2 -1.</_>
        <_>
          16 5 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 10 -1.</_>
        <_>
          14 10 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 4 6 -1.</_>
        <_>
          14 7 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 3 2 -1.</_>
        <_>
          15 6 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 3 4 -1.</_>
        <_>
          15 6 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 2 6 -1.</_>
        <_>
          15 6 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 6 2 -1.</_>
        <_>
          16 6 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 6 17 -1.</_>
        <_>
          16 6 2 17 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 8 2 13 -1.</_>
        <_>
          15 8 1 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 8 4 6 -1.</_>
        <_>
          14 10 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 2 -1.</_>
        <_>
          15 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 3 2 -1.</_>
        <_>
          15 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 4 -1.</_>
        <_>
          14 9 1 2 2.</_>
        <_>
          15 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 3 -1.</_>
        <_>
          15 9 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 4 1 -1.</_>
        <_>
          16 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 6 1 -1.</_>
        <_>
          16 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 9 9 -1.</_>
        <_>
          14 12 9 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 1 -1.</_>
        <_>
          15 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 1 -1.</_>
        <_>
          15 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          14 10 1 1 2.</_>
        <_>
          15 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          15 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 2 -1.</_>
        <_>
          15 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 3 -1.</_>
        <_>
          15 10 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 6 -1.</_>
        <_>
          14 10 1 3 2.</_>
        <_>
          15 13 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 12 6 2 -1.</_>
        <_>
          16 12 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 12 6 5 -1.</_>
        <_>
          16 12 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 14 8 6 -1.</_>
        <_>
          14 14 4 3 2.</_>
        <_>
          18 17 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 14 10 10 -1.</_>
        <_>
          14 14 5 5 2.</_>
        <_>
          19 19 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 16 10 8 -1.</_>
        <_>
          14 16 5 4 2.</_>
        <_>
          19 20 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 18 8 4 -1.</_>
        <_>
          14 18 4 2 2.</_>
        <_>
          18 20 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 19 3 4 -1.</_>
        <_>
          15 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 19 3 5 -1.</_>
        <_>
          15 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 20 3 4 -1.</_>
        <_>
          15 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 23 3 1 -1.</_>
        <_>
          15 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 0 8 1 -1.</_>
        <_>
          19 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 0 8 2 -1.</_>
        <_>
          19 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 2 2 10 -1.</_>
        <_>
          16 2 1 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 2 6 7 -1.</_>
        <_>
          17 2 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 2 5 3 -1.</_>
        <_>
          15 3 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 2 6 -1.</_>
        <_>
          16 4 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 2 8 -1.</_>
        <_>
          16 4 1 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 6 8 -1.</_>
        <_>
          18 4 3 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 8 3 -1.</_>
        <_>
          15 5 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 2 2 -1.</_>
        <_>
          16 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 2 -1.</_>
        <_>
          16 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 3 -1.</_>
        <_>
          16 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 6 -1.</_>
        <_>
          16 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 3 18 -1.</_>
        <_>
          15 12 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 6 7 -1.</_>
        <_>
          18 6 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 8 4 -1.</_>
        <_>
          15 6 4 2 2.</_>
        <_>
          19 8 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 3 6 -1.</_>
        <_>
          15 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 5 4 -1.</_>
        <_>
          15 9 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 5 6 -1.</_>
        <_>
          15 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 6 6 -1.</_>
        <_>
          15 9 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 7 3 -1.</_>
        <_>
          15 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 9 6 -1.</_>
        <_>
          15 9 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 2 -1.</_>
        <_>
          15 8 1 1 2.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 4 -1.</_>
        <_>
          15 8 1 2 2.</_>
        <_>
          16 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 1 12 -1.</_>
        <_>
          15 14 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 2 2 -1.</_>
        <_>
          15 9 1 1 2.</_>
        <_>
          16 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 4 -1.</_>
        <_>
          16 9 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 2 3 -1.</_>
        <_>
          15 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 7 3 -1.</_>
        <_>
          15 10 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 2 1 -1.</_>
        <_>
          16 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 4 -1.</_>
        <_>
          16 10 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 5 -1.</_>
        <_>
          16 10 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 12 4 8 -1.</_>
        <_>
          15 12 2 4 2.</_>
        <_>
          17 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 15 4 3 -1.</_>
        <_>
          15 16 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 16 5 3 -1.</_>
        <_>
          15 17 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 19 3 4 -1.</_>
        <_>
          16 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 19 9 3 -1.</_>
        <_>
          15 20 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 20 6 3 -1.</_>
        <_>
          18 20 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 1 -1.</_>
        <_>
          20 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 2 -1.</_>
        <_>
          20 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 4 -1.</_>
        <_>
          16 0 4 2 2.</_>
        <_>
          20 2 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 6 -1.</_>
        <_>
          16 0 4 3 2.</_>
        <_>
          20 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 8 -1.</_>
        <_>
          16 0 4 4 2.</_>
        <_>
          20 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 12 -1.</_>
        <_>
          16 0 4 6 2.</_>
        <_>
          20 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 1 4 13 -1.</_>
        <_>
          18 1 2 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 2 3 2 -1.</_>
        <_>
          17 2 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 3 2 3 -1.</_>
        <_>
          16 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 4 1 3 -1.</_>
        <_>
          16 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 4 2 2 -1.</_>
        <_>
          16 4 1 1 2.</_>
        <_>
          17 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 5 2 3 -1.</_>
        <_>
          17 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 2 9 -1.</_>
        <_>
          16 9 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 4 4 -1.</_>
        <_>
          18 6 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 3 9 -1.</_>
        <_>
          16 9 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 7 6 -1.</_>
        <_>
          16 8 7 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 1 6 -1.</_>
        <_>
          16 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 3 -1.</_>
        <_>
          16 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 6 -1.</_>
        <_>
          16 9 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 2 -1.</_>
        <_>
          16 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 3 -1.</_>
        <_>
          16 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 6 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 6 4 -1.</_>
        <_>
          16 7 3 2 2.</_>
        <_>
          19 9 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 3 -1.</_>
        <_>
          16 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 6 -1.</_>
        <_>
          16 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 2 -1.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 8 1 1 2.</_>
        <_>
          17 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 8 2 -1.</_>
        <_>
          16 8 4 1 2.</_>
        <_>
          20 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 3 1 -1.</_>
        <_>
          17 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 1 3 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 2 3 -1.</_>
        <_>
          17 9 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 4 1 -1.</_>
        <_>
          18 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 2 2 -1.</_>
        <_>
          16 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 4 4 -1.</_>
        <_>
          18 9 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 6 6 -1.</_>
        <_>
          16 9 3 3 2.</_>
        <_>
          19 12 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 2 -1.</_>
        <_>
          16 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 3 -1.</_>
        <_>
          16 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 2 -1.</_>
        <_>
          16 10 1 1 2.</_>
        <_>
          17 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 2 -1.</_>
        <_>
          17 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 5 -1.</_>
        <_>
          17 10 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 3 13 -1.</_>
        <_>
          17 10 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 3 -1.</_>
        <_>
          16 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 3 3 -1.</_>
        <_>
          16 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 1 2 -1.</_>
        <_>
          16 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          17 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 2 -1.</_>
        <_>
          16 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 3 -1.</_>
        <_>
          16 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 13 3 3 -1.</_>
        <_>
          16 14 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 14 4 1 -1.</_>
        <_>
          18 14 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 4 3 -1.</_>
        <_>
          18 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 6 2 -1.</_>
        <_>
          19 15 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 8 3 -1.</_>
        <_>
          20 15 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 16 4 1 -1.</_>
        <_>
          18 16 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 17 3 7 -1.</_>
        <_>
          17 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 17 6 3 -1.</_>
        <_>
          16 18 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 19 3 4 -1.</_>
        <_>
          17 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 0 6 1 -1.</_>
        <_>
          20 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 2 1 4 -1.</_>
        <_>
          17 4 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 2 -1.</_>
        <_>
          18 3 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 2 8 -1.</_>
        <_>
          17 3 1 4 2.</_>
        <_>
          18 7 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 3 -1.</_>
        <_>
          17 4 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 1 3 -1.</_>
        <_>
          17 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 2 2 -1.</_>
        <_>
          18 4 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 2 6 -1.</_>
        <_>
          17 4 1 3 2.</_>
        <_>
          18 7 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 1 6 -1.</_>
        <_>
          17 8 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 4 8 -1.</_>
        <_>
          17 6 2 4 2.</_>
        <_>
          19 10 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 3 -1.</_>
        <_>
          17 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 5 3 -1.</_>
        <_>
          17 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 3 -1.</_>
        <_>
          17 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 6 -1.</_>
        <_>
          17 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 6 -1.</_>
        <_>
          17 7 1 3 2.</_>
        <_>
          18 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 3 -1.</_>
        <_>
          17 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 6 4 -1.</_>
        <_>
          17 10 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 1 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 2 6 -1.</_>
        <_>
          17 9 1 3 2.</_>
        <_>
          18 12 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 4 2 -1.</_>
        <_>
          17 9 2 1 2.</_>
        <_>
          19 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 2 -1.</_>
        <_>
          17 10 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 5 3 -1.</_>
        <_>
          17 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 7 2 -1.</_>
        <_>
          17 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 1 3 -1.</_>
        <_>
          17 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 2 -1.</_>
        <_>
          17 10 1 1 2.</_>
        <_>
          18 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 4 -1.</_>
        <_>
          18 10 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 3 4 -1.</_>
        <_>
          18 10 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 2 -1.</_>
        <_>
          17 10 2 1 2.</_>
        <_>
          19 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 1 3 -1.</_>
        <_>
          17 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 2 -1.</_>
        <_>
          18 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 3 -1.</_>
        <_>
          18 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 2 2 -1.</_>
        <_>
          17 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 4 2 -1.</_>
        <_>
          17 11 2 1 2.</_>
        <_>
          19 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 12 3 2 -1.</_>
        <_>
          18 12 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 13 4 5 -1.</_>
        <_>
          19 13 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 14 2 3 -1.</_>
        <_>
          17 15 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 14 4 2 -1.</_>
        <_>
          19 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 2 -1.</_>
        <_>
          19 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 16 4 3 -1.</_>
        <_>
          19 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 17 3 7 -1.</_>
        <_>
          18 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 19 3 4 -1.</_>
        <_>
          18 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 21 3 3 -1.</_>
        <_>
          18 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 4 1 -1.</_>
        <_>
          20 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 1 -1.</_>
        <_>
          21 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 4 -1.</_>
        <_>
          21 0 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 1 1 12 -1.</_>
        <_>
          18 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 2 3 3 -1.</_>
        <_>
          19 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 3 2 -1.</_>
        <_>
          19 3 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 1 9 -1.</_>
        <_>
          18 6 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 3 4 -1.</_>
        <_>
          19 3 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 3 2 -1.</_>
        <_>
          19 4 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 3 4 -1.</_>
        <_>
          19 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 5 6 15 -1.</_>
        <_>
          21 5 3 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          18 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 3 3 -1.</_>
        <_>
          18 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 3 -1.</_>
        <_>
          18 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 1 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 2 2 -1.</_>
        <_>
          19 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 2 -1.</_>
        <_>
          18 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 1 3 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 2 -1.</_>
        <_>
          18 8 1 1 2.</_>
        <_>
          19 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 3 -1.</_>
        <_>
          18 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 3 14 -1.</_>
        <_>
          18 15 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 1 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 2 2 -1.</_>
        <_>
          18 9 1 1 2.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 2 -1.</_>
        <_>
          19 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 1 -1.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 2 -1.</_>
        <_>
          18 10 1 1 2.</_>
        <_>
          19 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 2 -1.</_>
        <_>
          18 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 6 4 -1.</_>
        <_>
          21 10 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 6 5 -1.</_>
        <_>
          21 10 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 2 -1.</_>
        <_>
          19 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 6 -1.</_>
        <_>
          19 11 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 9 -1.</_>
        <_>
          19 11 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 8 -1.</_>
        <_>
          18 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 3 4 -1.</_>
        <_>
          19 12 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 2 6 -1.</_>
        <_>
          18 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 6 2 -1.</_>
        <_>
          21 12 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 3 12 -1.</_>
        <_>
          18 16 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 13 3 1 -1.</_>
        <_>
          19 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 14 6 6 -1.</_>
        <_>
          21 14 3 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 20 3 4 -1.</_>
        <_>
          19 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 20 6 3 -1.</_>
        <_>
          18 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 2 2 4 -1.</_>
        <_>
          19 2 1 2 2.</_>
        <_>
          20 4 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 4 -1.</_>
        <_>
          19 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 20 -1.</_>
        <_>
          19 14 1 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 2 4 -1.</_>
        <_>
          19 6 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 4 3 -1.</_>
        <_>
          19 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 5 2 2 -1.</_>
        <_>
          19 5 1 1 2.</_>
        <_>
          20 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 3 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 2 3 -1.</_>
        <_>
          19 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 5 3 -1.</_>
        <_>
          19 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 5 9 -1.</_>
        <_>
          19 9 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 7 1 12 -1.</_>
        <_>
          19 11 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 7 2 3 -1.</_>
        <_>
          19 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 8 1 3 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 8 2 3 -1.</_>
        <_>
          20 8 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 2 1 -1.</_>
        <_>
          20 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 3 2 -1.</_>
        <_>
          20 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 2 2 -1.</_>
        <_>
          19 10 1 1 2.</_>
        <_>
          20 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 4 1 -1.</_>
        <_>
          21 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 7 -1.</_>
        <_>
          20 11 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 10 -1.</_>
        <_>
          20 11 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 11 -1.</_>
        <_>
          20 11 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 13 -1.</_>
        <_>
          20 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 14 3 10 -1.</_>
        <_>
          20 14 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 15 3 2 -1.</_>
        <_>
          19 16 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 18 3 3 -1.</_>
        <_>
          20 18 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 18 3 6 -1.</_>
        <_>
          20 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 20 5 3 -1.</_>
        <_>
          19 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 4 1 3 -1.</_>
        <_>
          20 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 1 2 -1.</_>
        <_>
          20 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 1 3 -1.</_>
        <_>
          20 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 2 3 -1.</_>
        <_>
          20 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 3 9 -1.</_>
        <_>
          20 8 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 6 4 9 -1.</_>
        <_>
          20 9 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 8 4 16 -1.</_>
        <_>
          22 8 2 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 9 4 6 -1.</_>
        <_>
          20 11 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 10 3 10 -1.</_>
        <_>
          21 10 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 10 3 9 -1.</_>
        <_>
          20 13 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 16 3 3 -1.</_>
        <_>
          21 16 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 17 3 7 -1.</_>
        <_>
          21 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 17 4 6 -1.</_>
        <_>
          20 19 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 18 3 3 -1.</_>
        <_>
          21 18 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 1 2 4 -1.</_>
        <_>
          21 3 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 5 1 3 -1.</_>
        <_>
          21 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 6 3 9 -1.</_>
        <_>
          21 9 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 10 3 3 -1.</_>
        <_>
          21 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 13 3 7 -1.</_>
        <_>
          22 13 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 3 -1.</_>
        <_>
          22 16 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 7 -1.</_>
        <_>
          22 16 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 17 3 5 -1.</_>
        <_>
          22 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 17 3 6 -1.</_>
        <_>
          21 19 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 17 3 6 -1.</_>
        <_>
          21 20 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 19 3 3 -1.</_>
        <_>
          22 19 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 19 3 5 -1.</_>
        <_>
          22 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 3 -1.</_>
        <_>
          22 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 11 2 3 -1.</_>
        <_>
          22 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 7 1 3 -1.</_>
        <_>
          23 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 9 1 3 -1.</_>
        <_>
          23 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 10 1 3 -1.</_>
        <_>
          23 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 14 1 9 -1.</_>
        <_>
          23 17 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 15 1 9 -1.</_>
        <_>
          23 18 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 18 1 6 -1.</_>
        <_>
          23 20 1 2 3.</_></rects>
      <tilted>0</tilted></_></features></cascade>
</opencv_storage>
