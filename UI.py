from nicegui import ui
from camera_integration import get_camera_manager, initialize_camera_system

class SlideScanner:
    def __init__(self):
        self.preview_visible = False
        self.zoom_slider_visible = False
        self.zoom_level = 1.0
        self.preview_popup = None
        self.zoom_popup = None
        self.zoom_slider = None
        self.zoom_label = None

def create_ui():
    # Create instance of SlideScanner class
    app = SlideScanner()

    # Add custom CSS for full screen layout and pop-ups
    ui.add_head_html('''
    <style>
        .main-stream {
            position: fixed;
            top: 45px;
            left: 0;
            width: 100vw;
            height: calc(100vh - 45px);
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            z-index: 1;
        }

        .preview-popup {
            position: fixed;
            top: 65px;
            right: 20px;
            width: 280px;
            height: 220px;
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: none;
            flex-direction: column;
            padding: 0;
            overflow: hidden;
        }

        .zoom-popup {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 250px;
            height: 100px;
            background: white;
            border: 2px solid #e74c3c;
            border-radius: 12px;
            z-index: 1000;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: none;
        }

        .close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #c0392b;
        }

        .popup-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
    </style>
    ''')

    # Create menu bar (smaller size)
    with ui.header().classes('bg-gray-800 text-white').style('height: 45px; min-height: 45px;'):
        with ui.row().classes('w-full justify-between items-center h-full'):
            ui.label('🔬 Slide Scanner').classes('text-base font-bold ml-2')

            with ui.row().classes('gap-1 mr-2'):
                # === File menu ===
                with ui.dropdown_button('File', icon='folder', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('New Project', on_click=lambda: ui.notify('New Project created'))
                    ui.item('Open Project', on_click=lambda: ui.notify('Open Project dialog'))
                    ui.item('Save Project', on_click=lambda: ui.notify('Project saved'))
                    ui.separator()
                    ui.item('Import Image', on_click=lambda: ui.notify('Import Image dialog'))
                    ui.item('Export Image', on_click=lambda: ui.notify('Export Image dialog'))
                    ui.separator()
                    ui.item('Exit', on_click=lambda: ui.notify('Exit application'))

                # === View menu ===
                with ui.dropdown_button('View', icon='visibility', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('Toggle Preview', on_click=lambda: toggle_preview(app))
                    ui.item('Toggle Zoom Slider', on_click=lambda: toggle_zoom_slider(app))
                    ui.separator()
                    ui.item('Full Screen', on_click=lambda: ui.notify('Full Screen mode activated'))
                    ui.item('Reset View', on_click=lambda: ui.notify('View reset to default'))

                # === Camera menu ===
                with ui.dropdown_button('Camera', icon='videocam', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('Capture Image', on_click=lambda: camera_manager.capture_image())
                    ui.separator()
                    ui.item('Stop Toupcam', on_click=lambda: camera_manager.stop_main_camera())
                    ui.item('Restart Toupcam', on_click=lambda: camera_manager.start_main_camera())
                    ui.separator()
                    ui.item('Stop USB Preview', on_click=lambda: camera_manager.stop_preview_camera())
                    ui.item('Restart USB Preview', on_click=lambda: camera_manager.start_preview_camera())

                # === Tools menu ===
                with ui.dropdown_button('Tools', icon='build', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('Calibration', on_click=lambda: ui.notify('Calibration tool opened'))
                    ui.item('Measurement', on_click=lambda: ui.notify('Measurement tool opened'))
                    ui.item('Annotation', on_click=lambda: ui.notify('Annotation tool opened'))
                    ui.separator()
                    ui.item('Image Analysis', on_click=lambda: ui.notify('Image Analysis tool'))

                # === Settings menu ===
                with ui.dropdown_button('Settings', icon='settings', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('Camera Settings', on_click=lambda: ui.notify('Camera Settings opened'))
                    ui.item('Display Settings', on_click=lambda: ui.notify('Display Settings opened'))
                    ui.item('Preferences', on_click=lambda: ui.notify('Preferences opened'))
                    ui.separator()
                    ui.item('Advanced Settings', on_click=lambda: ui.notify('Advanced Settings'))

                # === Help menu ===
                with ui.dropdown_button('Help', icon='help', auto_close=True).props('flat size=sm').classes('text-white text-sm'):
                    ui.item('User Manual', on_click=lambda: ui.notify('User Manual opened'))
                    ui.item('Keyboard Shortcuts', on_click=lambda: ui.notify('Keyboard Shortcuts'))
                    ui.item('Documentation', on_click=lambda: ui.notify('Documentation opened'))
                    ui.separator()
                    ui.item('About', on_click=lambda: ui.notify('About Slide Scanner v1.0'))

    # Initialize camera system
    camera_manager = get_camera_manager()
    initialize_camera_system()

    # Create main stream area
    with ui.element('div').classes('main-stream'):
        # Create main camera display
        camera_manager.create_main_stream_ui()

    # Create preview popup
    app.preview_popup = ui.element('div').classes('preview-popup')
    with app.preview_popup:
        ui.button('×', on_click=lambda: hide_preview(app)).classes('close-btn')
        ui.label('Preview Stream').classes('popup-title')
        # Create preview camera display
        camera_manager.create_preview_stream_ui()

    # Create zoom popup
    app.zoom_popup = ui.element('div').classes('zoom-popup')
    with app.zoom_popup:
        ui.button('×', on_click=lambda: hide_zoom_slider(app)).classes('close-btn')
        ui.label('Zoom Control').classes('popup-title')
        app.zoom_slider = ui.slider(min=0.1, max=5.0, value=1.0, step=0.1,
                                   on_change=lambda e: on_zoom_change(app, e))
        app.zoom_label = ui.label(f'Zoom: {app.zoom_level:.1f}x').classes('text-sm text-gray-600 mt-2')

def toggle_preview(app):
    """Toggle preview pop-up visibility"""
    app.preview_visible = not app.preview_visible
    if app.preview_visible:
        app.preview_popup.style('display: flex;')
        ui.notify('Preview enabled', type='positive')
    else:
        app.preview_popup.style('display: none;')
        ui.notify('Preview disabled', type='info')

def hide_preview(app):
    """Hide preview pop-up"""
    app.preview_visible = False
    app.preview_popup.style('display: none;')
    ui.notify('Preview closed', type='info')

def toggle_zoom_slider(app):
    """Toggle zoom slider pop-up visibility"""
    app.zoom_slider_visible = not app.zoom_slider_visible
    if app.zoom_slider_visible:
        app.zoom_popup.style('display: block;')
        ui.notify('Zoom slider enabled', type='positive')
    else:
        app.zoom_popup.style('display: none;')
        ui.notify('Zoom slider disabled', type='info')

def hide_zoom_slider(app):
    """Hide zoom slider pop-up"""
    app.zoom_slider_visible = False
    app.zoom_popup.style('display: none;')
    ui.notify('Zoom slider closed', type='info')

def on_zoom_change(app, e):
    """Handle zoom slider changes"""
    app.zoom_level = e.value
    app.zoom_label.text = f'Zoom: {app.zoom_level:.1f}x'
    ui.notify(f'Zoom level: {app.zoom_level:.1f}x', type='info')
