from nicegui import ui

class SlideScanner:
    def __init__(self):
        self.preview_visible = False
        self.zoom_slider_visible = False
        self.zoom_level = 1.0
        self.preview_popup = None
        self.zoom_popup = None
        self.zoom_slider = None
        self.zoom_label = None

def create_ui():
    # Create instance of SlideScanner class
    app = SlideScanner()

    # Add custom CSS for full screen layout and pop-ups
    ui.add_head_html('''
    <style>
        .main-stream {
            position: fixed;
            top: 60px;
            left: 0;
            width: 100vw;
            height: calc(100vh - 60px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            z-index: 1;
        }

        .preview-popup {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            height: 200px;
            background: white;
            border: 2px solid #3498db;
            border-radius: 12px;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: none;
            flex-direction: column;
            padding: 15px;
        }

        .zoom-popup {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 250px;
            height: 100px;
            background: white;
            border: 2px solid #e74c3c;
            border-radius: 12px;
            z-index: 1000;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: none;
        }

        .close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #c0392b;
        }

        .popup-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
    </style>
    ''')

    # Create menu bar
    with ui.header().classes('bg-gray-800 text-white'):
        with ui.row().classes('w-full justify-between items-center'):
            ui.label('🔬 Slide Scanner').classes('text-lg font-bold ml-2')

            with ui.row().classes('gap-4 mr-4'):
                # === File menu ===
                with ui.menu().props('anchor="top left" self="bottom left"') as file_menu:
                    ui.menu_item('New Project', lambda: ui.notify('New Project created'))
                    ui.menu_item('Open Project', lambda: ui.notify('Open Project dialog'))
                    ui.menu_item('Save Project', lambda: ui.notify('Project saved'))
                    ui.separator()
                    ui.menu_item('Import Image', lambda: ui.notify('Import Image dialog'))
                    ui.menu_item('Export Image', lambda: ui.notify('Export Image dialog'))
                    ui.separator()
                    ui.menu_item('Exit', lambda: ui.notify('Exit application'))
                ui.button('File', icon='folder').props('flat').classes('text-white').on('click', file_menu.open)

                # === View menu ===
                with ui.menu().props('anchor="top left" self="bottom left"') as view_menu:
                    ui.menu_item('Toggle Preview', lambda: toggle_preview(app))
                    ui.menu_item('Toggle Zoom Slider', lambda: toggle_zoom_slider(app))
                    ui.separator()
                    ui.menu_item('Full Screen', lambda: ui.notify('Full Screen mode activated'))
                    ui.menu_item('Reset View', lambda: ui.notify('View reset to default'))
                ui.button('View', icon='visibility').props('flat').classes('text-white').on('click', view_menu.open)

                # === Tools menu ===
                with ui.menu().props('anchor="top left" self="bottom left"') as tools_menu:
                    ui.menu_item('Calibration', lambda: ui.notify('Calibration tool opened'))
                    ui.menu_item('Measurement', lambda: ui.notify('Measurement tool opened'))
                    ui.menu_item('Annotation', lambda: ui.notify('Annotation tool opened'))
                    ui.separator()
                    ui.menu_item('Image Analysis', lambda: ui.notify('Image Analysis tool'))
                ui.button('Tools', icon='build').props('flat').classes('text-white').on('click', tools_menu.open)

                # === Settings menu ===
                with ui.menu().props('anchor="top left" self="bottom left"') as settings_menu:
                    ui.menu_item('Camera Settings', lambda: ui.notify('Camera Settings opened'))
                    ui.menu_item('Display Settings', lambda: ui.notify('Display Settings opened'))
                    ui.menu_item('Preferences', lambda: ui.notify('Preferences opened'))
                    ui.separator()
                    ui.menu_item('Advanced Settings', lambda: ui.notify('Advanced Settings'))
                ui.button('Settings', icon='settings').props('flat').classes('text-white').on('click', settings_menu.open)

                # === Help menu ===
                with ui.menu().props('anchor="top left" self="bottom left"') as help_menu:
                    ui.menu_item('User Manual', lambda: ui.notify('User Manual opened'))
                    ui.menu_item('Keyboard Shortcuts', lambda: ui.notify('Keyboard Shortcuts'))
                    ui.menu_item('Documentation', lambda: ui.notify('Documentation opened'))
                    ui.separator()
                    ui.menu_item('About', lambda: ui.notify('About Slide Scanner v1.0'))
                ui.button('Help', icon='help').props('flat').classes('text-white').on('click', help_menu.open)

    # Create main stream area
    with ui.element('div').classes('main-stream'):
        with ui.column().classes('items-center'):
            ui.icon('camera_alt', size='4rem').classes('text-white opacity-50 mb-4')
            ui.label('Main Stream Area').classes('text-3xl font-bold mb-2')
            ui.label('Camera feed will be displayed here').classes('text-lg opacity-75')
            ui.label('Use View menu to toggle Preview and Zoom controls').classes('text-sm opacity-50 mt-4')

    # Create preview popup
    app.preview_popup = ui.element('div').classes('preview-popup')
    with app.preview_popup:
        ui.button('×', on_click=lambda: hide_preview(app)).classes('close-btn')
        ui.label('Preview Stream').classes('popup-title')
        with ui.card().classes('w-full h-32 bg-gray-100 flex items-center justify-center'):
            ui.icon('preview', size='2rem').classes('text-gray-400')
            ui.label('Preview feed').classes('text-gray-500 ml-2')

    # Create zoom popup
    app.zoom_popup = ui.element('div').classes('zoom-popup')
    with app.zoom_popup:
        ui.button('×', on_click=lambda: hide_zoom_slider(app)).classes('close-btn')
        ui.label('Zoom Control').classes('popup-title')
        app.zoom_slider = ui.slider(min=0.1, max=5.0, value=1.0, step=0.1,
                                   on_change=lambda e: on_zoom_change(app, e))
        app.zoom_label = ui.label(f'Zoom: {app.zoom_level:.1f}x').classes('text-sm text-gray-600 mt-2')

def toggle_preview(app):
    """Toggle preview pop-up visibility"""
    app.preview_visible = not app.preview_visible
    if app.preview_visible:
        app.preview_popup.style('display: flex;')
        ui.notify('Preview enabled', type='positive')
    else:
        app.preview_popup.style('display: none;')
        ui.notify('Preview disabled', type='info')

def hide_preview(app):
    """Hide preview pop-up"""
    app.preview_visible = False
    app.preview_popup.style('display: none;')
    ui.notify('Preview closed', type='info')

def toggle_zoom_slider(app):
    """Toggle zoom slider pop-up visibility"""
    app.zoom_slider_visible = not app.zoom_slider_visible
    if app.zoom_slider_visible:
        app.zoom_popup.style('display: block;')
        ui.notify('Zoom slider enabled', type='positive')
    else:
        app.zoom_popup.style('display: none;')
        ui.notify('Zoom slider disabled', type='info')

def hide_zoom_slider(app):
    """Hide zoom slider pop-up"""
    app.zoom_slider_visible = False
    app.zoom_popup.style('display: none;')
    ui.notify('Zoom slider closed', type='info')

def on_zoom_change(app, e):
    """Handle zoom slider changes"""
    app.zoom_level = e.value
    app.zoom_label.text = f'Zoom: {app.zoom_level:.1f}x'
    ui.notify(f'Zoom level: {app.zoom_level:.1f}x', type='info')
