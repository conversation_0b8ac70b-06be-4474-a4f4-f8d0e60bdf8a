#!/usr/bin/env python3
"""
Camera Integration for Slide Scanner UI
Integrates camera streaming with NiceGUI interface
"""

import asyncio
from nicegui import ui
from camera_stream import CameraStream
from toupcam_stream import ToupcamStream

class CameraManager:
    def __init__(self):
        """Initialize camera manager"""
        self.main_camera = None  # Will be ToupcamStream
        self.preview_camera = None  # Will be CameraStream (OpenCV)
        self.is_main_streaming = False
        self.is_preview_streaming = False

        # UI elements
        self.main_image = None
        self.preview_image = None
        self.status_label = None
        self.fps_label = None
        
    def initialize_cameras(self, preview_camera_index: int = 0):
        """
        Initialize camera streams

        Args:
            preview_camera_index: Preview camera device index (USB camera)
        """
        try:
            # Initialize main camera (Toupcam)
            self.main_camera = ToupcamStream()

            # Initialize preview camera (USB camera via OpenCV)
            self.preview_camera = CameraStream(preview_camera_index)
            self.preview_camera.set_resolution(640, 480)  # Lower resolution for preview
            self.preview_camera.set_fps(15)

            print("Cameras initialized successfully")
            print("- Main camera: Toupcam SDK")
            print(f"- Preview camera: USB camera index {preview_camera_index}")
            return True

        except Exception as e:
            print(f"Error initializing cameras: {e}")
            return False
    
    def create_main_stream_ui(self):
        """
        Create main camera stream UI element (Toupcam)

        Returns:
            ui.image: Main camera display element
        """
        with ui.card().classes('w-full h-full bg-black flex items-center justify-center'):
            # Camera display
            self.main_image = ui.image().style(
                'width: 100%; height: 100%; object-fit: contain; max-height: calc(100vh - 120px);'
            )

            # Overlay controls
            with ui.element('div').style(
                'position: absolute; top: 10px; left: 10px; z-index: 10; background: rgba(0,0,0,0.8); padding: 12px; border-radius: 8px;'
            ):
                self.status_label = ui.label('Toupcam: Initializing...').classes('text-white text-sm font-bold')
                self.fps_label = ui.label('FPS: --').classes('text-green-400 text-xs mt-1')

                # Auto-start main camera
                ui.timer(1.0, self.auto_start_main_camera, once=True)

        return self.main_image
    
    def create_preview_stream_ui(self):
        """
        Create preview camera stream UI element (USB Camera)

        Returns:
            ui.image: Preview camera display element
        """
        with ui.card().classes('w-full h-full bg-gray-800 flex items-center justify-center'):
            ui.label('USB Camera Preview').classes('text-white text-sm mb-2 font-bold')

            self.preview_image = ui.image().style(
                'width: 100%; height: 150px; object-fit: contain; border-radius: 4px;'
            )

            # Auto-start preview camera
            ui.timer(2.0, self.auto_start_preview_camera, once=True)

        return self.preview_image
    
    async def update_main_stream(self):
        """Update main camera stream (Toupcam) (async)"""
        while self.is_main_streaming and self.main_camera:
            try:
                frame_data = self.main_camera.get_current_frame_base64()
                if frame_data and self.main_image:
                    self.main_image.set_source(frame_data)

                await asyncio.sleep(1/30)  # 30 FPS

            except Exception as e:
                print(f"Error updating main stream: {e}")
                break
    
    async def update_preview_stream(self):
        """Update preview camera stream (USB Camera) (async)"""
        while self.is_preview_streaming and self.preview_camera:
            try:
                frame_data = self.preview_camera.get_current_frame_base64()
                if frame_data and self.preview_image:
                    self.preview_image.set_source(frame_data)

                await asyncio.sleep(1/15)  # 15 FPS

            except Exception as e:
                print(f"Error updating preview stream: {e}")
                break
    
    def auto_start_main_camera(self):
        """Auto-start main camera on initialization"""
        self.start_main_camera()

    def start_main_camera(self):
        """Start main camera streaming (Toupcam)"""
        if not self.main_camera:
            ui.notify('Toupcam not initialized', type='negative')
            return

        # Initialize camera first
        if not self.main_camera.initialize_camera():
            self.status_label.text = 'Toupcam: Failed to initialize'
            ui.notify('Failed to initialize Toupcam', type='negative')
            return

        # Set callbacks
        self.main_camera.set_frame_callback(lambda frame: None)  # Frame handled by get_current_frame_base64
        self.main_camera.set_fps_callback(self.update_fps_display)
        self.main_camera.set_error_callback(self.handle_camera_error)

        # Start streaming
        if self.main_camera.start_stream():
            self.is_main_streaming = True
            self.status_label.text = 'Toupcam: Connected'

            # Get camera info
            info = self.main_camera.get_camera_info()
            if info:
                self.status_label.text = f"Toupcam: {info['name']} ({info['resolution']})"

            # Start async update loop
            asyncio.create_task(self.update_main_stream())

            ui.notify('Toupcam started successfully', type='positive')
        else:
            self.status_label.text = 'Toupcam: Failed to start'
            ui.notify('Failed to start Toupcam', type='negative')
    
    def update_fps_display(self, fps_text: str):
        """Update FPS display"""
        if self.fps_label:
            self.fps_label.text = fps_text

    def handle_camera_error(self, error_msg: str):
        """Handle camera error messages"""
        print(f"Camera Error: {error_msg}")
        if self.status_label:
            self.status_label.text = f"Toupcam: Error - {error_msg[:30]}..."
        ui.notify(f"Camera Error: {error_msg}", type='negative')

    def stop_main_camera(self):
        """Stop main camera streaming (Toupcam)"""
        if self.main_camera:
            self.is_main_streaming = False
            self.main_camera.stop_stream()
            self.status_label.text = 'Toupcam: Disconnected'
            self.fps_label.text = 'FPS: --'

            # Clear image
            if self.main_image:
                self.main_image.set_source('')

            ui.notify('Toupcam stopped', type='info')
    
    def auto_start_preview_camera(self):
        """Auto-start preview camera on initialization"""
        self.start_preview_camera()

    def start_preview_camera(self):
        """Start preview camera streaming (USB Camera)"""
        if not self.preview_camera:
            ui.notify('USB camera not initialized', type='negative')
            return

        # USB camera is always separate from Toupcam
        if self.preview_camera.start_stream():
            self.is_preview_streaming = True
            asyncio.create_task(self.update_preview_stream())
            ui.notify('USB camera preview started', type='positive')
        else:
            ui.notify('Failed to start USB camera preview', type='negative')
    
    def stop_preview_camera(self):
        """Stop preview camera streaming (USB Camera)"""
        self.is_preview_streaming = False

        if self.preview_camera:
            self.preview_camera.stop_stream()

        # Clear preview image
        if self.preview_image:
            self.preview_image.set_source('')

        ui.notify('USB camera preview stopped', type='info')

    def capture_image(self):
        """Capture current frame from Toupcam"""
        if self.main_camera and self.is_main_streaming:
            if self.main_camera.snap_image(0):  # Use highest resolution (index 0)
                ui.notify('Image captured from Toupcam!', type='positive')
            else:
                ui.notify('Failed to capture image', type='negative')
        else:
            ui.notify('Toupcam not running', type='warning')
    
    def cleanup(self):
        """Cleanup cameras on app shutdown"""
        self.is_main_streaming = False
        self.is_preview_streaming = False

        if self.main_camera:
            self.main_camera.stop_stream()

        if self.preview_camera:
            self.preview_camera.stop_stream()

        print("Camera manager cleanup completed")

# Global camera manager instance
camera_manager = CameraManager()

def initialize_camera_system():
    """Initialize the camera system"""
    return camera_manager.initialize_cameras(preview_camera_index=0)

def get_camera_manager():
    """Get the global camera manager instance"""
    return camera_manager
