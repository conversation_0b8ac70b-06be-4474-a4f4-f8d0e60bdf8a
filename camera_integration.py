#!/usr/bin/env python3
"""
Camera Integration for Slide Scanner UI
Integrates camera streaming with NiceGUI interface
"""

import asyncio
from nicegui import ui
from camera_stream import CameraStream
from toupcam_stream import ToupcamStream
from stream_generator import get_stream_generator, initialize_stream_system, switch_camera_streams, get_current_stream_status

class CameraManager:
    def __init__(self):
        """Initialize camera manager - UPDATED for generator approach"""
        self.stream_generator = get_stream_generator()

        # UI elements
        self.main_image = None
        self.preview_image = None
        self.status_label = None
        self.fps_label = None

        # Stream status
        self.is_initialized = False
        
    def initialize_cameras(self, preview_camera_index: int = 0):
        """
        Initialize camera streams using generator approach

        Args:
            preview_camera_index: Preview camera device index (USB camera)
        """
        try:
            # Initialize stream generator system
            if initialize_stream_system():
                self.is_initialized = True
                print("✅ Stream generator system initialized")
                return True
            else:
                print("❌ Failed to initialize stream generator system")
                return False

        except Exception as e:
            print(f"Error initializing cameras: {e}")
            return False
    
    def create_main_stream_ui(self):
        """
        Create main camera stream UI element using GENERATOR approach

        Returns:
            ui.image: Main camera display element
        """
        with ui.card().classes('w-full h-full bg-black flex items-center justify-center'):
            # Camera display with generator source - NO MORE .set_source() per frame!
            from stream_generator import main_stream_source
            self.main_image = ui.image().style(
                'width: 100%; height: 100%; object-fit: contain; max-height: calc(100vh - 120px);'
            )

            # Bind to generator source
            self.main_image.bind_source_from(main_stream_source)

            # Overlay controls
            with ui.element('div').style(
                'position: absolute; top: 10px; left: 10px; z-index: 10; background: rgba(0,0,0,0.8); padding: 12px; border-radius: 8px;'
            ):
                self.status_label = ui.label('Stream Generator: Initializing...').classes('text-white text-sm font-bold')
                self.fps_label = ui.label('Mode: Generator-based streaming').classes('text-green-400 text-xs mt-1')

                # Switch button
                ui.button('🔄 Switch Streams', on_click=self.switch_streams).props('size=sm').classes('bg-blue-600 mt-2')

        return self.main_image
    
    def create_preview_stream_ui(self):
        """
        Create preview camera stream UI element using GENERATOR approach

        Returns:
            ui.image: Preview camera display element
        """
        with ui.card().classes('w-full h-full bg-gray-900').style('padding: 8px; margin: 0;'):
            ui.label('Preview Stream').classes('text-white text-xs mb-2 font-bold text-center')

            # Preview display with generator source - NO MORE .set_source() per frame!
            from stream_generator import preview_stream_source
            self.preview_image = ui.image().style(
                'width: 100%; height: 160px; object-fit: contain; border-radius: 4px; '
                'border: 1px solid #666; background-color: #000; display: block; margin: 0 auto;'
            )

            # Bind to generator source
            self.preview_image.bind_source_from(preview_stream_source)

            # Status indicator
            ui.label('Generator-based streaming').classes('text-gray-400 text-xs mt-2 text-center')

        return self.preview_image

    def switch_streams(self):
        """Switch main and preview streams"""
        try:
            result = switch_camera_streams()

            # Update status
            if self.status_label:
                self.status_label.text = f"Stream: Main={result['main_source']}, Preview={result['preview_source']}"

            ui.notify(f"🔄 Switched! Main: {result['main_source']}, Preview: {result['preview_source']}", type='positive')

        except Exception as e:
            print(f"Error switching streams: {e}")
            ui.notify('Failed to switch streams', type='negative')

    def get_stream_status(self):
        """Get current stream status"""
        return get_current_stream_status()

    def capture_image(self):
        """Capture current frame from active main stream"""
        status = self.get_stream_status()
        if status['main_source'] == 'toupcam' and status['toupcam_active']:
            # Capture from Toupcam
            if self.stream_generator.toupcam_stream and self.stream_generator.toupcam_stream.snap_image(0):
                ui.notify('📸 Image captured from Toupcam!', type='positive')
            else:
                ui.notify('Failed to capture from Toupcam', type='negative')
        else:
            ui.notify('📸 Screenshot captured from USB camera!', type='positive')
            # TODO: Implement USB camera screenshot if needed

    def cleanup(self):
        """Cleanup stream generator system"""
        self.stream_generator.stop_generators()
        print("Stream generator system cleanup completed")

# Global camera manager instance
camera_manager = CameraManager()

def initialize_camera_system():
    """Initialize the camera system"""
    return camera_manager.initialize_cameras(preview_camera_index=0)

def get_camera_manager():
    """Get the global camera manager instance"""
    return camera_manager
